{"name": "strategy-creator-2.0", "private": true, "version": "2.0.0", "type": "module", "main": "electron/main.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron electron/main.cjs", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron electron/main.cjs\"", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "concurrently": "^9.2.0", "electron": "^37.2.2", "electron-builder": "^26.0.12", "typescript": "~5.7.2", "vite": "^6.2.0", "wait-on": "^8.0.3"}, "build": {"appId": "com.strategycreator.app", "productName": "Strategy Creator 2.0", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}