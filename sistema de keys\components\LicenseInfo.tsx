import React, { useState, useEffect } from 'react';
import CryptoJS from 'crypto-js';

interface LicenseInfoProps {
  isOpen: boolean;
  onClose: () => void;
  onDeactivate?: () => void;
}

// ⚠️ IMPORTANTE: Cambiar esta clave secreta por la de tu proyecto
const SECRET_KEY = 'StrategyCreator2025SecretKey';

// Función para obtener la MAC address (igual que en ActivationDialog)
const getMacAddress = async (): Promise<string> => {
  try {
    if (window.electronAPI && window.electronAPI.isElectron) {
      const macAddress = await window.electronAPI.getMacAddress();
      return macAddress;
    } else {
      // Fallback para navegadores
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Fingerprint', 2, 2);
      }
      
      const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL(),
        navigator.hardwareConcurrency || 'unknown',
        navigator.deviceMemory || 'unknown'
      ].join('|');
      
      const hash = CryptoJS.SHA256(fingerprint).toString();
      const macLike = hash.substring(0, 12).match(/.{2}/g)?.join(':').toUpperCase() || '00:00:00:00:00:00';
      
      return macLike;
    }
  } catch (error) {
    console.error('Error obteniendo MAC:', error);
    return '00:00:00:00:00:00';
  }
};

// Función para generar la clave esperada
const generateExpectedKey = (macAddress: string): string => {
  const hmac = CryptoJS.HmacSHA256(macAddress, SECRET_KEY);
  return hmac.toString().substring(0, 12).toUpperCase();
};

// Función para obtener información del sistema
const getSystemInfo = async () => {
  try {
    if (window.electronAPI && window.electronAPI.isElectron) {
      return await window.electronAPI.getSystemInfo();
    } else {
      return {
        platform: navigator.platform,
        arch: 'unknown',
        hostname: 'unknown',
        version: navigator.userAgent,
        macAddress: await getMacAddress()
      };
    }
  } catch (error) {
    return {
      platform: 'unknown',
      arch: 'unknown',
      hostname: 'unknown',
      version: 'unknown',
      macAddress: '00:00:00:00:00:00'
    };
  }
};

export const LicenseInfo: React.FC<LicenseInfoProps> = ({ isOpen, onClose, onDeactivate }) => {
  const [licenseData, setLicenseData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadLicenseInfo();
    }
  }, [isOpen]);

  const loadLicenseInfo = async () => {
    setIsLoading(true);
    try {
      const electronDetected = window.electronAPI && window.electronAPI.isElectron;
      setIsElectron(electronDetected);

      const systemInfo = await getSystemInfo();
      const macAddress = await getMacAddress();
      const expectedKey = generateExpectedKey(macAddress);
      
      // Obtener fecha de activación del localStorage
      const activationDate = localStorage.getItem('activation_date') || new Date().toISOString();
      
      const licenseInfo = {
        productName: 'Strategy Creator', // ⚠️ CAMBIAR: Nombre de tu producto
        version: '2.0.0', // ⚠️ CAMBIAR: Versión de tu producto
        licenseType: 'Activación por Hardware',
        macAddress: macAddress,
        expectedKey: expectedKey,
        activationDate: new Date(activationDate).toLocaleString(),
        systemInfo: systemInfo,
        isElectron: electronDetected,
        status: 'Activado'
      };

      setLicenseData(licenseInfo);
    } catch (error) {
      console.error('Error cargando información de licencia:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeactivate = () => {
    if (window.confirm('¿Está seguro de que desea desactivar el producto? Necesitará una nueva clave de activación para volver a usarlo.')) {
      if (onDeactivate) {
        onDeactivate();
      }
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m0 0a2 2 0 102 2m-2-2a2 2 0 012 2m0 0V9a2 2 0 002-2m-2 2H7a2 2 0 00-2-2" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-bold">Información de Licencia</h2>
                <p className="text-blue-100 text-sm">Sistema de Protección v1.1</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-blue-100 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando información de licencia...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Estado de Licencia */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                <div className="flex flex-col items-center">
                  <svg className="w-16 h-16 text-green-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-2xl font-bold text-green-800 mb-2">Producto Activado</h3>
                  <p className="text-green-600 mb-4">{licenseData?.productName} v{licenseData?.version}</p>
                  <p className="text-sm text-gray-600">Su software está correctamente activado y listo para usar</p>
                </div>
              </div>

              {/* Botones */}
              <div className="flex justify-center space-x-4 pt-4">
                <button
                  onClick={handleDeactivate}
                  className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors font-medium"
                >
                  Borrar Clave de Activación
                </button>
                <button
                  onClick={onClose}
                  className="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors font-medium"
                >
                  Cerrar
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
