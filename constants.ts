
import { UnitType, OtherType } from './types';

export const INITIAL_PALETTE_COLORS = [
    '#3b82f6', // blue
    '#ef4444', // red
    '#22c55e', // green
    '#eab308', // yellow
    '#8b5cf6', // violet
    '#ec4899', // pink
    '#64748b', // slate
];

export const UNIT_TYPES: UnitType[] = [
    UnitType.Sword,
];

export const OTHER_TYPES: { name: string, type: OtherType }[] = [
    { name: 'Mortar', type: OtherType.Mortar },
];

export const FONT_FACES = [
  'Arial',
  'Verdana',
  'Georgia',
  'Times New Roman',
  'Courier New',
  'Impact',
  'Brush Script MT',
  'Comic Sans MS'
];