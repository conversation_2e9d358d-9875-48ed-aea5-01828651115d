#!/usr/bin/env node
// Generador de claves de activación
// Sistema de protección reutilizable

import CryptoJS from 'crypto-js';

// ⚠️ IMPORTANTE: Cambiar esta clave secreta por la de tu proyecto
const SECRET_KEY = 'StrategyCreator2025SecretKey';

/**
 * Valida el formato de una dirección MAC
 * @param {string} mac - Dirección MAC a validar
 * @returns {boolean} - True si el formato es válido
 */
function validateMacFormat(mac) {
    // Patrón para MAC address: XX:XX:XX:XX:XX:XX o XX-XX-XX-XX-XX-XX
    const pattern = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
    return pattern.test(mac);
}

/**
 * Normaliza una dirección MAC al formato estándar
 * @param {string} mac - Dirección MAC a normalizar
 * @returns {string} - MAC normalizada en formato XX:XX:XX:XX:XX:XX
 */
function normalizeMac(mac) {
    return mac.trim().toUpperCase().replace(/-/g, ':');
}

/**
 * Genera una clave de activación para una dirección MAC específica
 * @param {string} macAddress - Dirección MAC del dispositivo
 * @returns {string} - Clave de activación de 12 caracteres hexadecimales
 */
function generateActivationKey(macAddress) {
    try {
        // Normalizar la dirección MAC
        const normalizedMac = normalizeMac(macAddress);
        
        // Validar formato
        if (!validateMacFormat(normalizedMac)) {
            throw new Error('Formato de MAC address inválido. Use XX:XX:XX:XX:XX:XX');
        }
        
        // Generar HMAC SHA256
        const hmac = CryptoJS.HmacSHA256(normalizedMac, SECRET_KEY);
        
        // Tomar los primeros 12 caracteres y convertir a mayúsculas
        const activationKey = hmac.toString().substring(0, 12).toUpperCase();
        
        return activationKey;
    } catch (error) {
        throw new Error(`Error generando clave de activación: ${error.message}`);
    }
}

/**
 * Valida una clave de activación contra una dirección MAC
 * @param {string} macAddress - Dirección MAC del dispositivo
 * @param {string} activationKey - Clave de activación a validar
 * @returns {boolean} - True si la clave es válida para la MAC
 */
function validateActivationKey(macAddress, activationKey) {
    try {
        const expectedKey = generateActivationKey(macAddress);
        return activationKey.toUpperCase() === expectedKey;
    } catch (error) {
        console.error('Error validando clave:', error);
        return false;
    }
}

/**
 * Genera claves de activación para múltiples direcciones MAC
 * @param {string[]} macAddresses - Array de direcciones MAC
 * @returns {Array} - Array de objetos con MAC, clave y estado
 */
function generateMultipleKeys(macAddresses) {
    return macAddresses.map(mac => {
        try {
            const key = generateActivationKey(mac);
            return {
                mac: normalizeMac(mac),
                key: key,
                valid: true,
                error: null
            };
        } catch (error) {
            return {
                mac: mac,
                key: null,
                valid: false,
                error: error.message
            };
        }
    });
}

// Ejemplo de uso si se ejecuta directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    console.log('=== Generador de Claves de Activación ===');
    console.log('Sistema de Protección Reutilizable v1.1\n');

    // Ejemplos de uso
    const ejemploMAC = '00:1B:44:11:3A:B7';
    
    try {
        const clave = generateActivationKey(ejemploMAC);
        console.log(`MAC: ${ejemploMAC}`);
        console.log(`Clave de Activación: ${clave}`);
        console.log(`Validación: ${validateActivationKey(ejemploMAC, clave) ? 'VÁLIDA' : 'INVÁLIDA'}\n`);
    } catch (error) {
        console.error('Error:', error.message);
    }

    // Ejemplo con múltiples MACs
    const ejemploMACs = [
        '00:1B:44:11:3A:B7',
        '08:00:27:12:34:56',
        'AA:BB:CC:DD:EE:FF'
    ];

    console.log('=== Generación Múltiple ===');
    const resultados = generateMultipleKeys(ejemploMACs);
    resultados.forEach(resultado => {
        if (resultado.valid) {
            console.log(`${resultado.mac} -> ${resultado.key}`);
        } else {
            console.log(`${resultado.mac} -> ERROR: ${resultado.error}`);
        }
    });
}

// Función principal para testing
function main() {
    const macAddress = 'D8:43:AE:6D:08:4E'; // MAC real obtenida de Electron
    console.log('=== Generador de Claves de Activación ===');
    console.log('Strategy Creator 2.0');
    console.log('');
    console.log('MAC Address:', macAddress);

    try {
        const activationKey = generateActivationKey(macAddress);
        console.log('Clave de Activación:', activationKey);
        console.log('');
        console.log('✅ Clave generada exitosamente');
        console.log('📋 Copie esta clave para activar la aplicación');
    } catch (error) {
        console.error('❌ Error generando clave:', error.message);
    }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export {
    generateActivationKey,
    validateActivationKey,
    generateMultipleKeys
};
