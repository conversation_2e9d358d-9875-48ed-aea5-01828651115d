@echo off
title Generador de Claves de Activacion

echo.
echo ===============================================================
echo              GENERADOR DE CLAVES DE ACTIVACION
echo                     Sistema Reutilizable
echo ===============================================================
echo.

echo Verificando Python...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python no esta instalado
    echo Instale Python desde: https://python.org/
    pause
    exit /b 1
)

echo OK: Python encontrado
echo.

echo Verificando dependencias...

pip show pyperclip >nul 2>&1
if %errorlevel% neq 0 (
    echo Instalando dependencias...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ERROR: No se pudieron instalar las dependencias
        pause
        exit /b 1
    )
)

echo OK: Dependencias verificadas
echo.

echo Iniciando generador de claves...
python activador.py

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Error ejecutando el generador
    pause
)

echo.
pause
