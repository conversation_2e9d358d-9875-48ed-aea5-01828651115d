
export const translations = {
  // App General
  app_title: { en: 'Battle Planner', es: 'Planificador\n de Batalla' },
  project_import_error: { en: 'Failed to load project file. It might be corrupted or in an invalid format.', es: 'Error al cargar el archivo del proyecto. Puede estar corrupto o en un formato no válido.' },

  // Toolbar Titles
  tools_title: { en: 'TOOLS', es: 'HERRAMIENTAS' },
  global_settings_title: { en: 'GLOBAL SETTINGS', es: 'AJUSTES GLOBALES' },
  color_title: { en: 'COLOR', es: 'COLOR' },
  selected_token_title: { en: 'SELECTED TOKEN', es: 'UNIDAD SELECCIONADA' },
  units_title: { en: 'UNITS', es: 'UNIDADES' },
  others_title: { en: 'OTHERS', es: 'OTROS' },
  history_title: { en: 'HISTORY', es: 'HISTORIAL' },

  // Tools
  tool_select: { en: 'Select (S)', es: 'Seleccionar (S)' },
  tool_move: { en: 'Move (M)', es: 'Mover (M)' },
  tool_clone: { en: 'Clone', es: 'Clonar' },
  tool_zoom: { en: 'Zoom (Z)', es: 'Zoom (Z)' },
  tool_enlarge: { en: 'Enlarge (E)', es: 'Agrandar (E)' },
  tool_text: { en: 'Text (T)', es: 'Texto (T)' },
  tool_eraser: { en: 'Eraser (B)', es: 'Borrador (B)' },
  tool_path: { en: 'Draw Path', es: 'Dibujar Ruta' },
  tool_arrow: { en: 'Draw Arrow (F)', es: 'Dibujar Flecha (F)' },
  tool_circle: { en: 'Draw Circle (C)', es: 'Dibujar Círculo (C)' },

  // Arrow Settings
  arrow_settings_title: { en: 'Arrow Settings', es: 'Ajustes de Flecha' },
  arrow_color_label: { en: 'Arrow Color', es: 'Color de Flecha' },
  arrow_start_width: { en: 'Start Width', es: 'Ancho Inicial' },
  arrow_end_width: { en: 'End Width', es: 'Ancho Final' },
  arrowhead_length: { en: 'Arrowhead Length', es: 'Largo de Punta' },
  arrowhead_width: { en: 'Arrowhead Width', es: 'Ancho de Punta' },
  animate_circle_title: { en: 'Animate Circle', es: 'Animar Círculo' },
  circle_size: { en: 'Circle Size', es: 'Tamaño Círculo' },
  circle_color: { en: 'Circle Color', es: 'Color Círculo' },
  animation_speed: { en: 'Animation Speed', es: 'Velocidad Animación' },
  preview_title: { en: 'Preview', es: 'Previsualización' },
  
  // Text Creator
  text_creator_title: { en: 'Text Creator', es: 'Creador de Texto' },
  text_creator_default_text: { en: 'Draggable Text', es: 'Texto Arrastrable' },
  save_preset: { en: 'Save Preset', es: 'Guardar Preset' },
  load_preset: { en: 'Load Preset', es: 'Cargar Preset' },
  no_saved_presets: { en: 'No saved presets.', es: 'No hay presets guardados.' },
  delete_preset_title: { en: 'Delete', es: 'Eliminar' },
  text_label: { en: 'Text', es: 'Texto' },
  font_label: { en: 'Font', es: 'Fuente' },
  outline_label: { en: 'Outline', es: 'Borde' },
  outline_colors_label: { en: 'Colors', es: 'Colores' },

  // Selected Token
  angle_label: { en: 'Angle', es: 'Ángulo' },
  speed_label: { en: 'Speed', es: 'Velocidad' },
  patrol_mode_label: { en: 'Patrol Mode', es: 'Modo Patrulla' },
  mirror_mode_label: { en: 'Mirror Mode', es: 'Modo Espejo' },
  clear_path_button: { en: 'Clear Selected Path', es: 'Limpiar Ruta Seleccionada' },
  token_preview_label: { en: 'Token Preview', es: 'Previsualización de Unidad' },
  outline_width_label: { en: 'Outline Width', es: 'Ancho de Borde' },

  // Global Settings
  all_tokens_size_label: { en: 'All Tokens Size', es: 'Tamaño de Unidades' },
  show_paths_label: { en: 'Show Paths', es: 'Mostrar Rutas' },

  // Color Palette
  color_1_label: { en: 'Color 1', es: 'Color 1' },
  color_2_label: { en: 'Color 2', es: 'Color 2' },
  color_change_prompt: { en: 'double-click to change', es: 'doble-clic para cambiar' },
  new_color_label: { en: 'New:', es: 'Nuevo:' },
  confirm_button: { en: 'OK', es: 'OK' },
  cancel_button: { en: 'Cancel', es: 'Cancelar' },

  // Units & Artillery
  upload_custom_unit_title: { en: 'Upload Custom Unit (PNG, GIF)', es: 'Subir Unidad Personalizada (PNG, GIF)' },
  upload_custom_other_title: { en: 'Upload Custom Other (PNG, GIF)', es: 'Subir Otro Personalizado (PNG, GIF)' },
  drag_to_delete_title: { en: 'Drag a custom item here to delete', es: 'Arrastra un elemento personalizado aquí para eliminar' },

  // History
  undo_button: { en: 'Undo', es: 'Deshacer' },
  redo_button: { en: 'Redo', es: 'Rehacer' },
  undo_title: { en: 'Undo (Ctrl+Z)', es: 'Deshacer (Ctrl+Z)' },
  redo_title: { en: 'Redo (Ctrl+Y)', es: 'Rehacer (Ctrl+Y)' },

  // Main Actions
  import_button: { en: 'Import', es: 'Importar' },
  export_button: { en: 'Export', es: 'Exportar' },
  map_button: { en: 'Map', es: 'Mapa' },
  clear_button: { en: 'Clear', es: 'Limpiar' },
  import_title: { en: 'Import Project', es: 'Importar Proyecto' },
  export_title: { en: 'Export Project', es: 'Exportar Proyecto' },
  upload_map_title: { en: 'Upload Map', es: 'Subir Mapa' },
  clear_all_title: { en: 'Clear All', es: 'Limpiar Todo' },
  
  // Unit Names
  unit_sword: { en: 'Sword', es: 'Espada' },
  unit_text: { en: 'Text', es: 'Texto' },
  unit_other: { en: 'Other', es: 'Otro' },
  unit_custom: { en: 'Custom', es: 'Personalizada' },
  unit_customother: { en: 'Custom Other', es: 'Otro Personalizado' },

  // Other Names
  other_mortar: { en: 'Mortar', es: 'Mortero' },

  // Annotation Names
  annotation_arrow: { en: 'Arrow', es: 'Flecha' },
  annotation_circle: { en: 'Circle', es: 'Círculo' },
  
  // Canvas
  canvas_upload_prompt: { en: 'Upload a map to begin your strategy', es: 'Sube un mapa para empezar tu estrategia' },
  canvas_reset_zoom: { en: 'Reset Zoom (Esc)', es: 'Reiniciar Zoom (Esc)' },

  // Layers Panel
  layers_title: { en: 'Layers', es: 'Capas' },
  layers_open: { en: 'Open Layers', es: 'Abrir Capas' },
  layers_collapse: { en: 'Collapse Layers', es: 'Cerrar Capas' },
  layers_create_group: { en: 'Create New Group', es: 'Crear Nuevo Grupo' },
  layers_new_group_default_name: { en: 'New Group', es: 'Grupo Nuevo' },
  layers_no_items: { en: 'No items on map.', es: 'No hay elementos en el mapa.' },
  layers_group_none: { en: 'None', es: 'Ninguno' },
  layers_assign_to_group: { en: 'Assign to group', es: 'Asignar a grupo' },
  layers_toggle_visibility: { en: 'Toggle Visibility', es: 'Alternar Visibilidad' },
  layers_delete_item: { en: 'Delete Item', es: 'Eliminar Elemento' },
  layers_play_animation: { en: 'Play Animation', es: 'Reproducir Animación' },
  layers_pause_animation: { en: 'Pause Animation', es: 'Pausar Animación' },
  layers_reset_position: { en: 'Reset Position', es: 'Reiniciar Posición' },
  layers_play_group_animation: { en: 'Play Group Animation', es: 'Reproducir Animación de Grupo' },
  layers_pause_group_animation: { en: 'Pause Group Animation', es: 'Pausar Animación de Grupo' },
  layers_reset_group_position: { en: 'Reset Group Position', es: 'Reiniciar Posición de Grupo' },

  // Rotation Control
  rotation_control_title: { en: 'Map Rotation', es: 'Rotación del Mapa' },
  rotation_control_auto: { en: 'Auto', es: 'Auto' },
  rotation_control_reset: { en: 'Reset Rotation', es: 'Reiniciar Rotación' },

  // Toolbar collapse
  toolbar_collapse: { en: 'Collapse Panel', es: 'Cerrar Panel' },
  toolbar_expand: { en: 'Expand Panel', es: 'Expandir Panel' },
};
