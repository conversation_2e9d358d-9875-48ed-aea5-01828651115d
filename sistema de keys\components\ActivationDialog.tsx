import React, { useState, useEffect } from 'react';
import CryptoJS from 'crypto-js';

interface ActivationDialogProps {
  onActivationSuccess: () => void;
}

// ⚠️ IMPORTANTE: Cambiar esta clave secreta por la de tu proyecto
const SECRET_KEY = 'Lucas2Derepredador2025';

// Función para obtener la dirección MAC real de la PC
const getMacAddress = async (): Promise<string> => {
  try {
    if (window.electronAPI && window.electronAPI.isElectron) {
      // Usar la API de Electron para obtener la MAC real
      const macAddress = await window.electronAPI.getMacAddress();
      console.log('MAC real obtenida desde Electron:', macAddress);
      return macAddress;
    } else {
      // Fallback para navegadores web (no recomendado para producción)
      console.warn('Ejecutándose en navegador web - usando identificador simulado');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Fingerprint', 2, 2);
      }
      
      const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL(),
        navigator.hardwareConcurrency || 'unknown',
        navigator.deviceMemory || 'unknown'
      ].join('|');
      
      // Generar un hash que simule una MAC address
      const hash = CryptoJS.SHA256(fingerprint).toString();
      // Formatear como MAC address (XX:XX:XX:XX:XX:XX)
      const macLike = hash.substring(0, 12).match(/.{2}/g)?.join(':').toUpperCase() || '00:00:00:00:00:00';
      
      return macLike;
    }
  } catch (error) {
    console.error('Error obteniendo dirección MAC:', error);
    return '00:00:00:00:00:00';
  }
};

// Función para generar la clave esperada
const generateExpectedKey = (macAddress: string): string => {
  const hmac = CryptoJS.HmacSHA256(macAddress, SECRET_KEY);
  return hmac.toString().substring(0, 12).toUpperCase();
};

// Función para validar la clave de activación
const validateActivationKey = (macAddress: string, inputKey: string): boolean => {
  const expectedKey = generateExpectedKey(macAddress);
  return inputKey.toUpperCase() === expectedKey;
};

export const ActivationDialog: React.FC<ActivationDialogProps> = ({ onActivationSuccess }) => {
  const [macAddress, setMacAddress] = useState<string>('');
  const [activationKey, setActivationKey] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isElectron, setIsElectron] = useState<boolean>(false);
  const [systemInfo, setSystemInfo] = useState<any>(null);

  useEffect(() => {
    const loadMacAddress = async () => {
      try {
        // Verificar si estamos en Electron
        const electronDetected = window.electronAPI && window.electronAPI.isElectron;
        setIsElectron(electronDetected);

        if (electronDetected) {
          // Obtener información completa del sistema
          const sysInfo = await window.electronAPI.getSystemInfo();
          setSystemInfo(sysInfo);
          console.log('Información del sistema:', sysInfo);
        }

        const mac = await getMacAddress();
        setMacAddress(mac);
      } catch (error) {
        console.error('Error cargando dirección MAC:', error);
        setError('Error obteniendo identificador del sistema');
      } finally {
        setIsLoading(false);
      }
    };

    loadMacAddress();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!activationKey.trim()) {
      setError('Por favor ingrese la clave de activación');
      return;
    }

    if (activationKey.length !== 12) {
      setError('La clave de activación debe tener exactamente 12 caracteres');
      return;
    }

    if (validateActivationKey(macAddress, activationKey)) {
      // Guardar información adicional
      localStorage.setItem('activation_key', activationKey.toUpperCase());
      onActivationSuccess();
    } else {
      setError('Clave de activación inválida');
    }
  };

  const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().replace(/[^A-F0-9]/g, '');
    if (value.length <= 12) {
      setActivationKey(value);
      setError('');
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando información del sistema...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Activación Requerida</h2>
          <p className="text-gray-600">Ingrese su clave de activación para continuar</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dirección Física de Red (MAC)
              {isElectron && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  ✓ MAC Real
                </span>
              )}
              {!isElectron && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  ⚠ Simulada
                </span>
              )}
            </label>
            <div className="relative">
              <input
                type="text"
                value={macAddress}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700 font-mono text-sm"
              />
              <button
                type="button"
                onClick={() => navigator.clipboard?.writeText(macAddress)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                title="Copiar MAC"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>
            </div>
            {isElectron && systemInfo && (
              <p className="text-xs text-green-600 mt-1">
                Sistema: {systemInfo.platform} | Equipo: {systemInfo.hostname}
              </p>
            )}
            {!isElectron && (
              <p className="text-xs text-yellow-600 mt-1">
                ⚠ Ejecutándose en navegador - Para usar MAC real, ejecute la aplicación de escritorio
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Clave de Activación (12 dígitos)
            </label>
            <input
              type="text"
              value={activationKey}
              onChange={handleKeyChange}
              placeholder="Ingrese su clave de activación"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-center text-lg tracking-wider"
              maxLength={12}
              autoComplete="off"
            />
            <p className="text-xs text-gray-500 mt-1">
              {activationKey.length}/12 caracteres
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex">
                <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={activationKey.length !== 12}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            Activar Aplicación
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Sistema de Protección v1.1
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Protegido por sistema de activación HMAC SHA256
          </p>
        </div>
      </div>
    </div>
  );
};
