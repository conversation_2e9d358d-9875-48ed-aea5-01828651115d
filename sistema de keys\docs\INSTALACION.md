# 📦 Guía de Instalación - Sistema de Protección

## 🚀 Instalación Rápida

### 1. <PERSON><PERSON>r Sistema a tu Proyecto
```bash
# Copiar toda la carpeta "sistema de keys" a tu proyecto
cp -r "sistema de keys" tu-proyecto/
```

### 2. Instalar Dependencias NPM
```bash
# En tu proyecto React/Electron
npm install crypto-js @types/crypto-js

# Para proyectos Electron
npm install --save-dev electron electron-builder
```

### 3. Configurar TypeScript (si usas TypeScript)
Agregar a tu `tsconfig.json`:
```json
{
  "compilerOptions": {
    "types": ["./sistema de keys/types/electron"]
  }
}
```

## 🔧 Configuración Inicial

### 1. Personalizar Clave Secreta
**⚠️ IMPORTANTE**: Cambiar en TODOS los archivos:

#### Archivos a modificar:
- `sistema de keys/components/ActivationDialog.tsx`
- `sistema de keys/components/LicenseInfo.tsx`
- `sistema de keys/utils/activation-generator.js`
- `sistema de keys/generadorkey/activador.py`

#### Cambio requerido:
```typescript
// Cambiar esta línea en todos los archivos:
const SECRET_KEY = 'Lucas2Derepredador2025';

// Por tu clave personalizada:
const SECRET_KEY = 'TuClaveSecretaUnica2025';
```

### 2. Personalizar Información de la Aplicación

#### En `ActivationDialog.tsx`:
```typescript
// Cambiar información del producto
productName: 'Tu Aplicación',
version: '1.0.0',
```

#### En `LicenseInfo.tsx`:
```typescript
// Cambiar información del producto
productName: 'Tu Aplicación',
version: '1.0.0',
```

#### En `electron/main.cjs`:
```javascript
// Cambiar título de ventana
title: 'Tu Aplicación v1.0',
```

#### En `electron/preload.cjs`:
```javascript
// Cambiar información de la app
appInfo: {
    name: 'Tu Aplicación',
    version: '1.0.0',
    platform: process.platform
}
```

#### En `generadorkey/activador.py`:
```python
# Cambiar información de la aplicación
self.APP_NAME = "Tu Aplicación"
self.APP_VERSION = "1.0.0"
```

## 🔗 Integración en tu Aplicación

### 1. Integración Básica en React
```typescript
// App.tsx
import React, { useState } from 'react';
import { ActivationDialog } from './sistema de keys/components/ActivationDialog';
import { LicenseIcon } from './sistema de keys/components/LicenseIcon';

function App() {
  const [isActivated, setIsActivated] = useState(() => {
    return localStorage.getItem('app_activated') === 'true';
  });

  const handleActivationSuccess = () => {
    setIsActivated(true);
    localStorage.setItem('app_activated', 'true');
    localStorage.setItem('activation_date', new Date().toISOString());
  };

  const handleDeactivate = () => {
    localStorage.removeItem('app_activated');
    localStorage.removeItem('activation_key');
    localStorage.removeItem('activation_date');
    setIsActivated(false);
  };

  if (!isActivated) {
    return <ActivationDialog onActivationSuccess={handleActivationSuccess} />;
  }

  return (
    <div className="app">
      {/* Tu aplicación aquí */}
      
      {/* Icono de licencia en la esquina */}
      <div className="fixed bottom-4 right-4">
        <LicenseIcon onDeactivate={handleDeactivate} />
      </div>
    </div>
  );
}

export default App;
```

### 2. Configuración de Electron

#### Copiar archivos de Electron:
```bash
# Copiar configuración de Electron
cp "sistema de keys/electron/main.cjs" electron/
cp "sistema de keys/electron/preload.cjs" electron/
```

#### Actualizar package.json:
```json
{
  "main": "electron/main.cjs",
  "scripts": {
    "electron": "electron electron/main.cjs",
    "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron electron/main.cjs\"",
    "electron-build": "npm run build && electron-builder"
  }
}
```

## 🎨 Configuración de Estilos

### Tailwind CSS (Recomendado)
Los componentes están diseñados para Tailwind CSS. Si no lo usas:

```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

### CSS Personalizado
Si prefieres CSS personalizado, puedes modificar las clases en los componentes:

```typescript
// Ejemplo: cambiar estilos en ActivationDialog.tsx
className="tu-clase-personalizada"
```

## 🧪 Verificación de Instalación

### 1. Probar Generador Python
```bash
cd "sistema de keys/generadorkey"
ejecutar.bat
```

### 2. Probar Generador JavaScript
```bash
node "sistema de keys/utils/activation-generator.js"
```

### 3. Probar Integración React
1. Borrar localStorage: `localStorage.clear()`
2. Recargar aplicación
3. Debe aparecer diálogo de activación
4. Generar clave para tu MAC
5. Ingresar clave y verificar activación

## 🔍 Solución de Problemas

### Error: "crypto-js not found"
```bash
npm install crypto-js @types/crypto-js
```

### Error: "electronAPI is not defined"
- Verificar que `preload.cjs` esté configurado
- Verificar que `contextIsolation: true` en main.cjs

### Error: "MAC address inválida"
- Verificar formato: XX:XX:XX:XX:XX:XX
- Usar aplicación Electron para MAC real

### Error: "Clave inválida"
- Verificar que SECRET_KEY sea igual en todos los archivos
- Verificar que MAC sea exactamente la misma

## 📋 Checklist de Instalación

- [ ] ✅ Copiar carpeta "sistema de keys"
- [ ] ✅ Instalar dependencias NPM
- [ ] ✅ Cambiar SECRET_KEY en todos los archivos
- [ ] ✅ Personalizar información de la aplicación
- [ ] ✅ Integrar componentes en App.tsx
- [ ] ✅ Configurar Electron (si aplica)
- [ ] ✅ Probar generador de claves
- [ ] ✅ Probar activación/desactivación
- [ ] ✅ Verificar persistencia en localStorage

## 🎯 Próximos Pasos

1. **Generar claves**: Usar generador Python para crear claves
2. **Distribuir**: Crear ejecutables con electron-builder
3. **Documentar**: Crear manual de usuario para activación
4. **Soporte**: Preparar proceso para generar nuevas claves

---

**¡Sistema listo para usar!** 🚀

Para más información, consulta:
- `CONFIGURACION.md` - Configuración avanzada
- `EJEMPLOS.md` - Ejemplos de uso
- `README.md` - Documentación general
