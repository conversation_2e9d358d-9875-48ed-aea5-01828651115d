#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generador de Claves de Activación - Sistema Reutilizable
Herramienta independiente para generar claves de activación basadas en MAC address
⚠️ IMPORTANTE: Cambiar SECRET_KEY por la clave de tu proyecto
"""

import hashlib
import hmac
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import re
import pyperclip
import json
import os
from datetime import datetime

class GeneradorClaves:
    def __init__(self):
        # ⚠️ IMPORTANTE: Cambiar esta clave secreta por la de tu proyecto
        self.SECRET_KEY = "Lucas2Derepredador2025"
        
        # ⚠️ CAMBIAR: Información de tu aplicación
        self.APP_NAME = "Tu Aplicación"
        self.APP_VERSION = "1.0.0"
        
        # Configurar ventana principal
        self.root = tk.Tk()
        self.root.title(f"Generador de Claves - {self.APP_NAME} v{self.APP_VERSION}")
        self.root.geometry("600x700")
        self.root.resizable(True, True)
        
        # Configurar icono (opcional)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # Variables
        self.mac_var = tk.StringVar()
        self.clave_var = tk.StringVar()
        self.historial = []
        
        # Cargar historial si existe
        self.cargar_historial()
        
        # Crear interfaz
        self.crear_interfaz()
        
        # Centrar ventana
        self.centrar_ventana()
    
    def centrar_ventana(self):
        """Centrar la ventana en la pantalla"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def crear_interfaz(self):
        """Crear la interfaz gráfica"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(main_frame, text="🔐 Generador de Claves de Activación", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text=f"{self.APP_NAME} v{self.APP_VERSION}", 
                                  font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # Entrada de MAC
        ttk.Label(main_frame, text="Dirección MAC:", font=("Arial", 10, "bold")).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        mac_frame = ttk.Frame(main_frame)
        mac_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        mac_frame.columnconfigure(0, weight=1)
        
        self.mac_entry = ttk.Entry(mac_frame, textvariable=self.mac_var, font=("Consolas", 11))
        self.mac_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        self.mac_entry.bind('<KeyRelease>', self.on_mac_change)
        
        ttk.Button(mac_frame, text="Limpiar", command=self.limpiar_mac).grid(row=0, column=1)
        
        # Información de formato
        info_label = ttk.Label(main_frame, text="Formato: XX:XX:XX:XX:XX:XX (ejemplo: D8:43:AE:6D:08:4E)", 
                              font=("Arial", 9), foreground="gray")
        info_label.grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=(0, 15))
        
        # Botón generar
        self.btn_generar = ttk.Button(main_frame, text="🔑 Generar Clave de Activación", 
                                     command=self.generar_clave, style="Accent.TButton")
        self.btn_generar.grid(row=5, column=0, columnspan=3, pady=(0, 20))
        
        # Resultado
        ttk.Label(main_frame, text="Clave de Activación:", font=("Arial", 10, "bold")).grid(
            row=6, column=0, sticky=tk.W, pady=(0, 5))
        
        result_frame = ttk.Frame(main_frame)
        result_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        result_frame.columnconfigure(0, weight=1)
        
        self.clave_entry = ttk.Entry(result_frame, textvariable=self.clave_var, 
                                    font=("Consolas", 14, "bold"), state="readonly")
        self.clave_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.btn_copiar = ttk.Button(result_frame, text="📋 Copiar", command=self.copiar_clave)
        self.btn_copiar.grid(row=0, column=1)
        
        # Validación
        self.validation_label = ttk.Label(main_frame, text="", font=("Arial", 9))
        self.validation_label.grid(row=8, column=0, columnspan=3, pady=(0, 15))
        
        # Historial
        ttk.Label(main_frame, text="Historial de Claves Generadas:", font=("Arial", 10, "bold")).grid(
            row=9, column=0, sticky=tk.W, pady=(10, 5))
        
        # Frame para historial con scrollbar
        hist_frame = ttk.Frame(main_frame)
        hist_frame.grid(row=10, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        hist_frame.columnconfigure(0, weight=1)
        hist_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(10, weight=1)
        
        self.historial_text = scrolledtext.ScrolledText(hist_frame, height=8, font=("Consolas", 9))
        self.historial_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Botones del historial
        hist_buttons = ttk.Frame(main_frame)
        hist_buttons.grid(row=11, column=0, columnspan=3, pady=(5, 0))
        
        ttk.Button(hist_buttons, text="🗑️ Limpiar Historial", 
                  command=self.limpiar_historial).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(hist_buttons, text="💾 Exportar Historial", 
                  command=self.exportar_historial).pack(side=tk.LEFT)
        
        # Actualizar historial
        self.actualizar_historial_display()
    
    def validar_mac(self, mac):
        """Validar formato de dirección MAC"""
        # Patrón para MAC address: XX:XX:XX:XX:XX:XX
        patron = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
        return re.match(patron, mac) is not None
    
    def normalizar_mac(self, mac):
        """Normalizar MAC address a formato estándar"""
        # Remover espacios y convertir a mayúsculas
        mac = mac.strip().upper()
        # Reemplazar guiones con dos puntos
        mac = mac.replace('-', ':')
        return mac
    
    def generar_clave_activacion(self, mac_address):
        """Generar clave de activación usando HMAC SHA256"""
        try:
            # Normalizar MAC
            mac_normalizada = self.normalizar_mac(mac_address)
            
            # Validar formato
            if not self.validar_mac(mac_normalizada):
                raise ValueError("Formato de MAC address inválido")
            
            # Generar HMAC SHA256
            hmac_obj = hmac.new(
                self.SECRET_KEY.encode('utf-8'),
                mac_normalizada.encode('utf-8'),
                hashlib.sha256
            )
            
            # Tomar los primeros 12 caracteres y convertir a mayúsculas
            clave = hmac_obj.hexdigest()[:12].upper()
            
            return clave, mac_normalizada
            
        except Exception as e:
            raise Exception(f"Error generando clave: {str(e)}")
    
    def on_mac_change(self, event=None):
        """Manejar cambios en el campo MAC"""
        mac = self.mac_var.get()
        if mac:
            try:
                mac_normalizada = self.normalizar_mac(mac)
                if self.validar_mac(mac_normalizada):
                    self.validation_label.config(text="✅ Formato válido", foreground="green")
                    self.btn_generar.config(state="normal")
                else:
                    self.validation_label.config(text="❌ Formato inválido", foreground="red")
                    self.btn_generar.config(state="disabled")
            except:
                self.validation_label.config(text="❌ Formato inválido", foreground="red")
                self.btn_generar.config(state="disabled")
        else:
            self.validation_label.config(text="")
            self.btn_generar.config(state="disabled")
    
    def generar_clave(self):
        """Generar y mostrar clave de activación"""
        try:
            mac = self.mac_var.get()
            if not mac:
                messagebox.showerror("Error", "Por favor ingrese una dirección MAC")
                return
            
            clave, mac_normalizada = self.generar_clave_activacion(mac)
            self.clave_var.set(clave)
            
            # Agregar al historial
            entrada_historial = {
                "fecha": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "mac": mac_normalizada,
                "clave": clave
            }
            self.historial.append(entrada_historial)
            self.guardar_historial()
            self.actualizar_historial_display()
            
            # Mostrar mensaje de éxito
            messagebox.showinfo("Éxito", f"Clave generada exitosamente:\n\nMAC: {mac_normalizada}\nClave: {clave}")
            
        except Exception as e:
            messagebox.showerror("Error", str(e))
    
    def copiar_clave(self):
        """Copiar clave al portapapeles"""
        clave = self.clave_var.get()
        if clave:
            try:
                pyperclip.copy(clave)
                messagebox.showinfo("Copiado", f"Clave '{clave}' copiada al portapapeles")
            except:
                messagebox.showwarning("Advertencia", "No se pudo copiar al portapapeles")
    
    def limpiar_mac(self):
        """Limpiar campo MAC"""
        self.mac_var.set("")
        self.clave_var.set("")
        self.validation_label.config(text="")
    
    def actualizar_historial_display(self):
        """Actualizar la visualización del historial"""
        self.historial_text.delete(1.0, tk.END)
        
        if not self.historial:
            self.historial_text.insert(tk.END, "No hay claves generadas aún...\n")
            return
        
        self.historial_text.insert(tk.END, "FECHA                MAC                 CLAVE\n")
        self.historial_text.insert(tk.END, "=" * 60 + "\n")
        
        for entrada in reversed(self.historial[-20:]):  # Mostrar últimas 20
            linea = f"{entrada['fecha']} {entrada['mac']} {entrada['clave']}\n"
            self.historial_text.insert(tk.END, linea)
    
    def limpiar_historial(self):
        """Limpiar historial de claves"""
        if messagebox.askyesno("Confirmar", "¿Está seguro de que desea limpiar el historial?"):
            self.historial = []
            self.guardar_historial()
            self.actualizar_historial_display()
    
    def exportar_historial(self):
        """Exportar historial a archivo"""
        if not self.historial:
            messagebox.showwarning("Advertencia", "No hay historial para exportar")
            return
        
        try:
            filename = f"historial_claves_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"HISTORIAL DE CLAVES DE ACTIVACIÓN\n")
                f.write(f"{self.APP_NAME} v{self.APP_VERSION}\n")
                f.write("=" * 50 + "\n\n")
                
                for entrada in self.historial:
                    f.write(f"Fecha: {entrada['fecha']}\n")
                    f.write(f"MAC:   {entrada['mac']}\n")
                    f.write(f"Clave: {entrada['clave']}\n")
                    f.write("-" * 30 + "\n")
            
            messagebox.showinfo("Exportado", f"Historial exportado a: {filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error exportando historial: {str(e)}")
    
    def cargar_historial(self):
        """Cargar historial desde archivo"""
        try:
            if os.path.exists("historial.json"):
                with open("historial.json", 'r', encoding='utf-8') as f:
                    self.historial = json.load(f)
        except:
            self.historial = []
    
    def guardar_historial(self):
        """Guardar historial a archivo"""
        try:
            with open("historial.json", 'w', encoding='utf-8') as f:
                json.dump(self.historial, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def ejecutar(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()

def main():
    """Función principal"""
    try:
        app = GeneradorClaves()
        app.ejecutar()
    except Exception as e:
        print(f"Error iniciando aplicación: {e}")
        input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
