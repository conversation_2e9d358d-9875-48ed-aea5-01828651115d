// Declaraciones de tipos para la integración con Electron
// Sistema de protección reutilizable

export interface SystemInfo {
  platform: string;
  arch: string;
  hostname: string;
  version: string;
  macAddress: string;
}

export interface AppInfo {
  name: string;
  version: string;
  platform: string;
}

export interface ElectronAPI {
  // Obtener la dirección MAC real de la PC
  getMacAddress: () => Promise<string>;
  
  // Obtener información completa del sistema
  getSystemInfo: () => Promise<SystemInfo>;
  
  // Verificar si estamos en Electron
  isElectron: boolean;
  
  // Información de la aplicación
  appInfo: AppInfo;
}

// Extender la interfaz Window para incluir electronAPI
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
