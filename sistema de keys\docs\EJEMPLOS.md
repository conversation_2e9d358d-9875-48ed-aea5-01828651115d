# 💡 Ejemplos de Uso - Sistema de Protección

## 🚀 Ejemplo 1: Integración Básica en React

### App.tsx Completo
```typescript
import React, { useState, useEffect } from 'react';
import { ActivationDialog } from './sistema de keys/components/ActivationDialog';
import { LicenseIcon } from './sistema de keys/components/LicenseIcon';
import './App.css';

function App() {
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Verificar activación al cargar
  useEffect(() => {
    const checkActivation = () => {
      try {
        const activated = localStorage.getItem('app_activated') === 'true';
        setIsActivated(activated);
      } catch (error) {
        console.error('Error verificando activación:', error);
        setIsActivated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkActivation();
  }, []);

  const handleActivationSuccess = () => {
    setIsActivated(true);
    localStorage.setItem('app_activated', 'true');
    localStorage.setItem('activation_date', new Date().toISOString());
    
    // Opcional: mostrar mensaje de bienvenida
    alert('¡Aplicación activada exitosamente!');
  };

  const handleDeactivate = () => {
    localStorage.removeItem('app_activated');
    localStorage.removeItem('activation_key');
    localStorage.removeItem('activation_date');
    setIsActivated(false);
  };

  // Pantalla de carga
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Cargando aplicación...</p>
        </div>
      </div>
    );
  }

  // Diálogo de activación
  if (!isActivated) {
    return <ActivationDialog onActivationSuccess={handleActivationSuccess} />;
  }

  // Aplicación principal
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">Mi Aplicación</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">v1.0.0</span>
              <LicenseIcon onDeactivate={handleDeactivate} />
            </div>
          </div>
        </div>
      </header>

      {/* Contenido principal */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                ¡Aplicación Activada!
              </h2>
              <p className="text-gray-600">
                Tu contenido principal va aquí
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer con icono de licencia */}
      <footer className="fixed bottom-4 right-4">
        <LicenseIcon 
          onDeactivate={handleDeactivate}
          className="bg-white shadow-lg rounded-full p-2"
        />
      </footer>
    </div>
  );
}

export default App;
```

## 🖥️ Ejemplo 2: Aplicación Electron Completa

### main.cjs Personalizado
```javascript
const { app, BrowserWindow, ipcMain, Menu } = require('electron');
const path = require('path');
const os = require('os');

// Configuración de la aplicación
const APP_CONFIG = {
  name: 'Mi Aplicación',
  version: '1.0.0',
  width: 1200,
  height: 800,
  minWidth: 800,
  minHeight: 600
};

function getRealMacAddress() {
  // ... (código igual que en el archivo original)
}

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: APP_CONFIG.width,
    height: APP_CONFIG.height,
    minWidth: APP_CONFIG.minWidth,
    minHeight: APP_CONFIG.minHeight,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: `${APP_CONFIG.name} v${APP_CONFIG.version}`,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'default',
    backgroundColor: '#ffffff'
  });

  // Crear menú personalizado
  const template = [
    {
      label: 'Archivo',
      submenu: [
        {
          label: 'Salir',
          accelerator: 'CmdOrCtrl+Q',
          click: () => app.quit()
        }
      ]
    },
    {
      label: 'Ayuda',
      submenu: [
        {
          label: 'Acerca de',
          click: () => {
            // Mostrar información de la aplicación
            const { dialog } = require('electron');
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Acerca de',
              message: `${APP_CONFIG.name} v${APP_CONFIG.version}`,
              detail: 'Aplicación protegida con sistema de activación'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // Cargar aplicación
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  return mainWindow;
}

// Eventos de la aplicación
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

// IPC Handlers
ipcMain.handle('get-mac-address', getRealMacAddress);
ipcMain.handle('get-system-info', async () => ({
  platform: os.platform(),
  arch: os.arch(),
  hostname: os.hostname(),
  version: os.version(),
  macAddress: getRealMacAddress()
}));
```

## 🐍 Ejemplo 3: Generador Python Personalizado

### activador_personalizado.py
```python
#!/usr/bin/env python3
import hashlib
import hmac
import tkinter as tk
from tkinter import ttk, messagebox
import pyperclip

class MiGeneradorClaves:
    def __init__(self):
        # Configuración personalizada
        self.SECRET_KEY = "MiClaveSecreta2025"
        self.APP_NAME = "Mi Aplicación Personalizada"
        self.APP_VERSION = "2.0.0"
        
        self.root = tk.Tk()
        self.root.title(f"Generador - {self.APP_NAME}")
        self.root.geometry("500x400")
        
        # Variables
        self.mac_var = tk.StringVar()
        self.clave_var = tk.StringVar()
        
        self.crear_interfaz_personalizada()
    
    def crear_interfaz_personalizada(self):
        # Frame principal con estilo personalizado
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo/Título personalizado
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="🔐", font=("Arial", 24)).pack()
        ttk.Label(title_frame, text=self.APP_NAME, 
                 font=("Arial", 14, "bold")).pack()
        ttk.Label(title_frame, text=f"Generador de Claves v{self.APP_VERSION}", 
                 font=("Arial", 10)).pack()
        
        # Entrada MAC con validación en tiempo real
        ttk.Label(main_frame, text="Dirección MAC:", 
                 font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        mac_frame = ttk.Frame(main_frame)
        mac_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.mac_entry = ttk.Entry(mac_frame, textvariable=self.mac_var, 
                                  font=("Consolas", 11))
        self.mac_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.mac_entry.bind('<KeyRelease>', self.validar_mac_tiempo_real)
        
        ttk.Button(mac_frame, text="Limpiar", 
                  command=self.limpiar).pack(side=tk.RIGHT)
        
        # Indicador de validación
        self.validation_label = ttk.Label(main_frame, text="", 
                                         font=("Arial", 9))
        self.validation_label.pack(anchor=tk.W)
        
        # Botón generar con estilo
        self.btn_generar = ttk.Button(main_frame, text="🔑 Generar Clave", 
                                     command=self.generar_clave,
                                     state=tk.DISABLED)
        self.btn_generar.pack(pady=20)
        
        # Resultado con botón copiar
        ttk.Label(main_frame, text="Clave Generada:", 
                 font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        result_frame = ttk.Frame(main_frame)
        result_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.clave_entry = ttk.Entry(result_frame, textvariable=self.clave_var, 
                                    font=("Consolas", 12, "bold"), 
                                    state="readonly")
        self.clave_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.btn_copiar = ttk.Button(result_frame, text="📋", 
                                    command=self.copiar_clave,
                                    state=tk.DISABLED)
        self.btn_copiar.pack(side=tk.RIGHT)
        
        # Información adicional
        info_frame = ttk.LabelFrame(main_frame, text="Información", padding="10")
        info_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Label(info_frame, text="• Formato MAC: XX:XX:XX:XX:XX:XX", 
                 font=("Arial", 9)).pack(anchor=tk.W)
        ttk.Label(info_frame, text="• Clave: 12 caracteres hexadecimales", 
                 font=("Arial", 9)).pack(anchor=tk.W)
        ttk.Label(info_frame, text="• Algoritmo: HMAC SHA256", 
                 font=("Arial", 9)).pack(anchor=tk.W)
    
    def validar_mac_tiempo_real(self, event=None):
        mac = self.mac_var.get().upper()
        
        if len(mac) == 0:
            self.validation_label.config(text="", foreground="black")
            self.btn_generar.config(state=tk.DISABLED)
            return
        
        # Validar formato mientras escribe
        import re
        if re.match(r'^([0-9A-F]{2}[:-]){5}[0-9A-F]{2}$', mac):
            self.validation_label.config(text="✓ Formato válido", foreground="green")
            self.btn_generar.config(state=tk.NORMAL)
        else:
            self.validation_label.config(text="✗ Formato inválido", foreground="red")
            self.btn_generar.config(state=tk.DISABLED)
    
    def generar_clave(self):
        try:
            mac = self.mac_var.get().upper().replace('-', ':')
            
            # Generar HMAC
            hmac_obj = hmac.new(
                self.SECRET_KEY.encode('utf-8'),
                mac.encode('utf-8'),
                hashlib.sha256
            )
            
            clave = hmac_obj.hexdigest()[:12].upper()
            self.clave_var.set(clave)
            self.btn_copiar.config(state=tk.NORMAL)
            
            # Mostrar resultado
            messagebox.showinfo("Éxito", 
                f"Clave generada para:\nMAC: {mac}\nClave: {clave}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error generando clave: {e}")
    
    def copiar_clave(self):
        try:
            pyperclip.copy(self.clave_var.get())
            messagebox.showinfo("Copiado", "Clave copiada al portapapeles")
        except:
            messagebox.showwarning("Advertencia", "No se pudo copiar")
    
    def limpiar(self):
        self.mac_var.set("")
        self.clave_var.set("")
        self.btn_copiar.config(state=tk.DISABLED)
    
    def ejecutar(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = MiGeneradorClaves()
    app.ejecutar()
```

## 🔧 Ejemplo 4: Validación Avanzada

### Componente con Validación Extendida
```typescript
// AdvancedActivationDialog.tsx
import React, { useState, useEffect } from 'react';
import CryptoJS from 'crypto-js';

interface AdvancedActivationProps {
  onActivationSuccess: () => void;
  maxAttempts?: number;
  lockoutTime?: number; // minutos
}

export const AdvancedActivationDialog: React.FC<AdvancedActivationProps> = ({
  onActivationSuccess,
  maxAttempts = 3,
  lockoutTime = 15
}) => {
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockoutEnd, setLockoutEnd] = useState<Date | null>(null);
  const [timeRemaining, setTimeRemaining] = useState(0);

  // Verificar bloqueo al cargar
  useEffect(() => {
    const checkLockout = () => {
      const lockoutData = localStorage.getItem('activation_lockout');
      if (lockoutData) {
        const lockout = JSON.parse(lockoutData);
        const endTime = new Date(lockout.endTime);
        
        if (new Date() < endTime) {
          setIsLocked(true);
          setLockoutEnd(endTime);
          setAttempts(lockout.attempts);
        } else {
          // Limpiar bloqueo expirado
          localStorage.removeItem('activation_lockout');
        }
      }
    };

    checkLockout();
  }, []);

  // Contador de tiempo restante
  useEffect(() => {
    if (isLocked && lockoutEnd) {
      const interval = setInterval(() => {
        const remaining = Math.max(0, lockoutEnd.getTime() - Date.now());
        setTimeRemaining(Math.ceil(remaining / 1000));
        
        if (remaining <= 0) {
          setIsLocked(false);
          setLockoutEnd(null);
          setAttempts(0);
          localStorage.removeItem('activation_lockout');
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isLocked, lockoutEnd]);

  const handleFailedAttempt = () => {
    const newAttempts = attempts + 1;
    setAttempts(newAttempts);

    if (newAttempts >= maxAttempts) {
      // Activar bloqueo
      const endTime = new Date(Date.now() + lockoutTime * 60 * 1000);
      const lockoutData = {
        attempts: newAttempts,
        endTime: endTime.toISOString()
      };
      
      localStorage.setItem('activation_lockout', JSON.stringify(lockoutData));
      setIsLocked(true);
      setLockoutEnd(endTime);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLocked) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0-6V7m0 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-red-900 mb-2">Acceso Bloqueado</h2>
            <p className="text-gray-600 mb-4">
              Demasiados intentos fallidos. Intente nuevamente en:
            </p>
            <div className="text-3xl font-mono font-bold text-red-600 mb-4">
              {formatTime(timeRemaining)}
            </div>
            <p className="text-sm text-gray-500">
              Intentos realizados: {attempts}/{maxAttempts}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Resto del componente de activación normal...
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      {/* Componente de activación con contador de intentos */}
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
        {/* ... resto del JSX ... */}
        
        {attempts > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-sm text-yellow-700">
              ⚠️ Intentos fallidos: {attempts}/{maxAttempts}
              {attempts === maxAttempts - 1 && (
                <span className="block mt-1 font-semibold">
                  ¡Último intento antes del bloqueo!
                </span>
              )}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
```

## 📱 Ejemplo 5: Integración con API Externa

### Validación con Servidor
```typescript
// ServerValidationService.ts
export class ServerValidationService {
  private static readonly API_URL = 'https://tu-api.com/validate';
  
  static async validateWithServer(mac: string, key: string): Promise<boolean> {
    try {
      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mac, key })
      });
      
      const result = await response.json();
      return result.valid === true;
    } catch (error) {
      console.error('Error validando con servidor:', error);
      // Fallback a validación local
      return this.validateLocally(mac, key);
    }
  }
  
  private static validateLocally(mac: string, key: string): boolean {
    // Validación local como respaldo
    const SECRET_KEY = 'TuClaveSecreta2025';
    const hmac = CryptoJS.HmacSHA256(mac, SECRET_KEY);
    const expectedKey = hmac.toString().substring(0, 12).toUpperCase();
    return key.toUpperCase() === expectedKey;
  }
}
```

---

**¡Ejemplos listos para implementar!** 🎯

Estos ejemplos muestran diferentes formas de integrar y personalizar el sistema de protección según tus necesidades específicas.
