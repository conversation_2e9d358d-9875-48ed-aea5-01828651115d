# 🔐 Sistema de Protección por Claves de Activación

## 📋 Descripción
Sistema completo de protección por claves de activación basado en HMAC SHA256 y dirección MAC real del hardware. Diseñado para ser reutilizable en cualquier proyecto React/Electron.

## ✨ Características
- ✅ **Encriptación HMAC SHA256** con clave secreta personalizable
- ✅ **Vinculación por hardware** usando dirección MAC real
- ✅ **Aplicación Electron** para acceso completo al sistema
- ✅ **Interfaz de usuario** profesional y fácil de usar
- ✅ **Generador independiente** en Python con interfaz gráfica
- ✅ **Verificación continua** de licencia
- ✅ **Fácil integración** en proyectos existentes

## 🏗️ Estructura del Sistema

```
sistema de keys/
├── components/              # Componentes React
│   ├── ActivationDialog.tsx # Diálogo de activación
│   ├── LicenseInfo.tsx      # Modal de información de licencia
│   └── LicenseIcon.tsx      # Icono de licencia
├── electron/                # Configuración Electron
│   ├── main.cjs            # Proceso principal
│   └── preload.cjs         # Script de preload
├── utils/                   # Utilidades JavaScript
│   └── activation-generator.js # Generador de claves JS
├── generadorkey/            # Generador Python independiente
│   ├── activador.py        # Aplicación Python con GUI
│   ├── requirements.txt    # Dependencias Python
│   └── ejecutar.bat        # Script de ejecución
├── types/                   # Tipos TypeScript
│   └── electron.d.ts       # Declaraciones Electron
├── docs/                    # Documentación
│   ├── INSTALACION.md      # Guía de instalación
│   ├── CONFIGURACION.md    # Guía de configuración
│   └── EJEMPLOS.md         # Ejemplos de uso
└── README.md               # Este archivo
```

## 🚀 Instalación Rápida

### 1. Copiar Archivos
```bash
# Copiar toda la carpeta "sistema de keys" a tu proyecto
cp -r "sistema de keys" tu-proyecto/
```

### 2. Instalar Dependencias
```bash
# En tu proyecto React/Electron
npm install crypto-js @types/crypto-js electron electron-builder
```

### 3. Configurar Clave Secreta
```typescript
// En todos los archivos, cambiar la clave secreta:
const SECRET_KEY = 'TuClaveSecretaPersonalizada2025';
```

### 4. Integrar Componentes
```typescript
// En tu App.tsx
import { ActivationDialog } from './sistema de keys/components/ActivationDialog';
import { LicenseIcon } from './sistema de keys/components/LicenseIcon';

// Usar en tu aplicación
{!isActivated && <ActivationDialog onActivationSuccess={handleActivation} />}
<LicenseIcon onDeactivate={handleDeactivate} />
```

## 🔧 Configuración

### Personalizar Clave Secreta
Cambiar en todos los archivos la constante:
```typescript
const SECRET_KEY = 'TuClaveSecretaUnica2025';
```

### Personalizar Información del Producto
```typescript
// En LicenseInfo.tsx
productName: 'Tu Aplicación',
version: '1.0.0',
```

### Configurar Electron
```javascript
// En electron/main.cjs
title: 'Tu Aplicación v1.0',
```

## 🎯 Uso Básico

### 1. Integración en React
```typescript
import React, { useState } from 'react';
import { ActivationDialog } from './sistema de keys/components/ActivationDialog';
import { LicenseIcon } from './sistema de keys/components/LicenseIcon';

function App() {
  const [isActivated, setIsActivated] = useState(() => {
    return localStorage.getItem('app_activated') === 'true';
  });

  const handleActivationSuccess = () => {
    setIsActivated(true);
    localStorage.setItem('app_activated', 'true');
  };

  const handleDeactivate = () => {
    localStorage.removeItem('app_activated');
    setIsActivated(false);
  };

  if (!isActivated) {
    return <ActivationDialog onActivationSuccess={handleActivationSuccess} />;
  }

  return (
    <div className="app">
      {/* Tu aplicación aquí */}
      <LicenseIcon onDeactivate={handleDeactivate} />
    </div>
  );
}
```

### 2. Generar Claves de Activación

#### Opción A: Generador Python (Recomendado)
```bash
cd "sistema de keys/generadorkey"
ejecutar.bat
```

#### Opción B: Generador JavaScript
```bash
node "sistema de keys/utils/activation-generator.js"
```

#### Opción C: Programáticamente
```javascript
import { generateActivationKey } from './sistema de keys/utils/activation-generator.js';

const macAddress = 'D8:43:AE:6D:08:4E';
const activationKey = generateActivationKey(macAddress);
console.log(`Clave: ${activationKey}`);
```

## 🔑 Algoritmo de Protección

### Generación de Claves
1. **Obtener MAC real** del adaptador de red principal
2. **Aplicar HMAC SHA256** con clave secreta
3. **Extraer 12 caracteres** hexadecimales
4. **Convertir a mayúsculas** para formato final

### Validación
1. **Verificar formato** de clave (12 caracteres hex)
2. **Obtener MAC actual** del dispositivo
3. **Generar clave esperada** con mismo algoritmo
4. **Comparar claves** para validar

## 🛡️ Seguridad

### Fortalezas
- **HMAC SHA256**: Algoritmo criptográfico robusto
- **MAC real**: Vinculación física al hardware
- **Clave secreta**: Integrada en el código
- **Validación local**: No requiere conexión a internet

### Consideraciones
- **Clave visible**: En código fuente (ofuscar para mayor seguridad)
- **localStorage**: Puede ser borrado por el usuario
- **MAC simulada**: En navegadores web (usar Electron para MAC real)

## 📦 Dependencias

### React/TypeScript
```json
{
  "dependencies": {
    "crypto-js": "^4.2.0",
    "@types/crypto-js": "^4.2.2"
  }
}
```

### Electron
```json
{
  "devDependencies": {
    "electron": "^37.2.1",
    "electron-builder": "^26.0.12"
  }
}
```

### Python (Generador)
```
pyperclip==1.8.2
```

## 🎨 Personalización de UI

### Colores y Estilos
Los componentes usan Tailwind CSS. Personalizar en:
- `ActivationDialog.tsx` - Diálogo de activación
- `LicenseInfo.tsx` - Modal de información
- `LicenseIcon.tsx` - Icono de licencia

### Textos e Idiomas
Cambiar textos directamente en los componentes:
```typescript
// Ejemplo en ActivationDialog.tsx
<h2>Tu Título Personalizado</h2>
<p>Tu mensaje personalizado</p>
```

## 🧪 Testing

### Probar Activación
1. Borrar localStorage: `localStorage.clear()`
2. Recargar aplicación
3. Ingresar clave válida
4. Verificar activación exitosa

### Probar Desactivación
1. Hacer clic en icono de licencia
2. Hacer clic en "Borrar Clave"
3. Confirmar desactivación
4. Verificar vuelta al diálogo

## 📞 Soporte

### Generar Claves
- Usar generador Python para interfaz gráfica
- Usar generador JavaScript para automatización
- MAC format: XX:XX:XX:XX:XX:XX

### Problemas Comunes
- **Clave inválida**: Verificar MAC exacta
- **No persiste**: Verificar localStorage habilitado
- **MAC incorrecta**: Usar Electron para MAC real

## 🔄 Actualizaciones

### Cambiar Clave Secreta
1. Actualizar en todos los archivos
2. Regenerar todas las claves existentes
3. Distribuir nuevas claves a usuarios

### Agregar Funcionalidades
- Modificar componentes según necesidades
- Mantener compatibilidad con algoritmo base
- Documentar cambios para futuros proyectos

## 📄 Licencia
Sistema de protección reutilizable para proyectos personales y comerciales.

---

**¡Sistema listo para integrar en cualquier proyecto!** 🚀
