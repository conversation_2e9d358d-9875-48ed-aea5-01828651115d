# ⚙️ Configuración Avanzada - Sistema de Protección

## 🔐 Configuración de Seguridad

### 1. Personalización de Clave Secreta

#### Generar Clave Secreta Fuerte
```javascript
// Ejemplo de clave secreta robusta
const SECRET_KEY = 'MiApp2025_' + btoa(Math.random().toString()).substring(0, 20);

// O usar una frase personalizada
const SECRET_KEY = 'MiAplicacionSecreta_Version2025_ClaveUnica';
```

#### Mejores Prácticas
- **Longitud**: Mínimo 20 caracteres
- **Complejidad**: Incluir números, letras y símbolos
- **Unicidad**: Diferente para cada proyecto
- **Secreto**: No compartir públicamente

### 2. Ofuscación de Código (Opcional)

#### Para JavaScript/TypeScript:
```bash
# Instalar herramienta de ofuscación
npm install --save-dev javascript-obfuscator

# Ofuscar archivos críticos
javascript-obfuscator sistema\ de\ keys/utils/activation-generator.js --output sistema\ de\ keys/utils/activation-generator.obfuscated.js
```

#### Para Python:
```bash
# Instalar PyInstaller para crear ejecutable
pip install pyinstaller

# Crear ejecutable del generador
pyinstaller --onefile --windowed activador.py
```

## 🎨 Personalización de Interfaz

### 1. Modificar Estilos de Componentes

#### ActivationDialog.tsx:
```typescript
// Cambiar colores principales
const primaryColor = "blue-600";  // Cambiar por tu color
const accentColor = "green-500";  // Color de éxito

// Personalizar título
<h2 className="text-2xl font-bold text-gray-900 mb-2">
  Tu Título Personalizado
</h2>

// Personalizar descripción
<p className="text-gray-600">
  Tu mensaje personalizado de activación
</p>
```

#### LicenseInfo.tsx:
```typescript
// Personalizar modal de licencia
<h3 className="text-2xl font-bold text-green-800 mb-2">
  Tu Mensaje de Activación
</h3>

// Cambiar información mostrada
<p className="text-green-600 mb-4">
  {licenseData?.productName} v{licenseData?.version}
</p>
```

### 2. Agregar Logo/Iconos Personalizados

#### Agregar logo en ActivationDialog:
```typescript
// Reemplazar icono por logo
<div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
  <img src="/tu-logo.png" alt="Logo" className="w-12 h-12" />
</div>
```

#### Personalizar icono de licencia:
```typescript
// En LicenseIcon.tsx, cambiar el SVG por tu icono
<img src="/tu-icono-licencia.png" alt="Licencia" className="w-5 h-5" />
```

## 🔧 Configuración de Electron

### 1. Configuración Avanzada de Ventana

#### main.cjs personalizado:
```javascript
const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'Tu Aplicación v1.0',
    show: false,
    autoHideMenuBar: true,
    // Configuraciones adicionales
    resizable: true,
    maximizable: true,
    fullscreenable: false,
    titleBarStyle: 'default', // 'hidden' para barra personalizada
    backgroundColor: '#ffffff'
});
```

### 2. Configuración de Empaquetado

#### package.json avanzado:
```json
{
  "build": {
    "appId": "com.tuempresa.tuapp",
    "productName": "Tu Aplicación",
    "directories": {
      "output": "release"
    },
    "files": [
      "dist/**/*",
      "electron/**/*",
      "node_modules/**/*"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico",
      "requestedExecutionLevel": "asInvoker"
    },
    "mac": {
      "target": "dmg",
      "icon": "assets/icon.icns"
    },
    "linux": {
      "target": "AppImage",
      "icon": "assets/icon.png"
    }
  }
}
```

## 📊 Configuración de Logging y Monitoreo

### 1. Agregar Sistema de Logs

#### En ActivationDialog.tsx:
```typescript
// Agregar logging de eventos
const logActivationAttempt = (mac: string, success: boolean) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    mac: mac,
    success: success,
    userAgent: navigator.userAgent
  };
  
  // Guardar en localStorage o enviar a servidor
  const logs = JSON.parse(localStorage.getItem('activation_logs') || '[]');
  logs.push(logEntry);
  localStorage.setItem('activation_logs', JSON.stringify(logs.slice(-100))); // Mantener últimos 100
};
```

### 2. Monitoreo de Uso

#### Agregar métricas básicas:
```typescript
// En App.tsx
useEffect(() => {
  // Registrar inicio de sesión
  const sessionStart = {
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    platform: navigator.platform
  };
  
  const sessions = JSON.parse(localStorage.getItem('app_sessions') || '[]');
  sessions.push(sessionStart);
  localStorage.setItem('app_sessions', JSON.stringify(sessions.slice(-50)));
}, []);
```

## 🔄 Configuración de Actualizaciones

### 1. Versionado de Sistema de Protección

#### Agregar versión en componentes:
```typescript
// Constante de versión del sistema
const PROTECTION_VERSION = '1.1.0';

// Verificar compatibilidad
const checkProtectionVersion = () => {
  const storedVersion = localStorage.getItem('protection_version');
  if (storedVersion !== PROTECTION_VERSION) {
    // Migrar datos si es necesario
    migrateProtectionData(storedVersion, PROTECTION_VERSION);
    localStorage.setItem('protection_version', PROTECTION_VERSION);
  }
};
```

### 2. Migración de Datos

#### Función de migración:
```typescript
const migrateProtectionData = (oldVersion: string, newVersion: string) => {
  // Ejemplo: migrar de v1.0 a v1.1
  if (oldVersion === '1.0.0' && newVersion === '1.1.0') {
    // Actualizar formato de datos si es necesario
    const oldData = localStorage.getItem('old_activation_data');
    if (oldData) {
      // Convertir al nuevo formato
      const newData = convertToNewFormat(oldData);
      localStorage.setItem('activation_data', newData);
      localStorage.removeItem('old_activation_data');
    }
  }
};
```

## 🌐 Configuración Multi-idioma

### 1. Agregar Soporte de Idiomas

#### Crear archivo de traducciones:
```typescript
// translations.ts
export const translations = {
  es: {
    activation_required: 'Activación Requerida',
    enter_key: 'Ingrese su clave de activación',
    invalid_key: 'Clave de activación inválida',
    product_activated: 'Producto Activado'
  },
  en: {
    activation_required: 'Activation Required',
    enter_key: 'Enter your activation key',
    invalid_key: 'Invalid activation key',
    product_activated: 'Product Activated'
  }
};
```

#### Usar traducciones en componentes:
```typescript
// En ActivationDialog.tsx
import { translations } from './translations';

const lang = localStorage.getItem('app_language') || 'es';
const t = translations[lang];

// Usar en JSX
<h2>{t.activation_required}</h2>
<p>{t.enter_key}</p>
```

## 🔒 Configuración de Seguridad Avanzada

### 1. Validación Adicional

#### Agregar verificación de tiempo:
```typescript
const validateActivationWithTime = (mac: string, key: string): boolean => {
  // Validación básica
  if (!validateActivationKey(mac, key)) {
    return false;
  }
  
  // Verificar que no haya pasado mucho tiempo desde la activación
  const activationDate = localStorage.getItem('activation_date');
  if (activationDate) {
    const daysSinceActivation = (Date.now() - new Date(activationDate).getTime()) / (1000 * 60 * 60 * 24);
    
    // Opcional: revalidar cada 30 días
    if (daysSinceActivation > 30) {
      return revalidateActivation(mac, key);
    }
  }
  
  return true;
};
```

### 2. Protección contra Manipulación

#### Verificar integridad de datos:
```typescript
const verifyDataIntegrity = (): boolean => {
  try {
    const activationData = {
      key: localStorage.getItem('activation_key'),
      date: localStorage.getItem('activation_date'),
      mac: localStorage.getItem('stored_mac')
    };
    
    // Generar checksum de los datos
    const dataString = JSON.stringify(activationData);
    const checksum = CryptoJS.SHA256(dataString + SECRET_KEY).toString();
    const storedChecksum = localStorage.getItem('data_checksum');
    
    return checksum === storedChecksum;
  } catch {
    return false;
  }
};
```

## 📱 Configuración Responsive

### 1. Adaptar para Diferentes Tamaños

#### CSS responsive para diálogos:
```css
/* Agregar en tu CSS */
@media (max-width: 640px) {
  .activation-dialog {
    margin: 1rem;
    padding: 1rem;
  }
  
  .activation-input {
    font-size: 16px; /* Evitar zoom en iOS */
  }
}
```

#### Componente responsive:
```typescript
// En ActivationDialog.tsx
const [isMobile, setIsMobile] = useState(window.innerWidth < 640);

useEffect(() => {
  const handleResize = () => setIsMobile(window.innerWidth < 640);
  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
}, []);

// Usar en JSX
<div className={`${isMobile ? 'p-4' : 'p-8'} max-w-md w-full mx-4`}>
```

---

**¡Configuración avanzada completada!** 🎯

Para más información:
- `INSTALACION.md` - Instalación básica
- `EJEMPLOS.md` - Ejemplos prácticos
- `README.md` - Documentación general
