{"name": "tu-aplicacion", "private": true, "version": "1.0.0", "description": "Tu aplicación con sistema de protección integrado", "author": "Tu Nombre", "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron electron/main.cjs", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron electron/main.cjs\"", "electron-build": "npm run build && electron-builder --win --x64", "dist": "npm run build && electron-builder --publish=never --win --x64", "build-exe": "npm run build && electron-builder --win portable --x64", "clean": "rimraf dist release"}, "dependencies": {"@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "postcss": "^8.5.0", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "terser": "^5.36.0", "typescript": "^5.7.2", "vite": "^6.3.5", "wait-on": "^8.0.1"}, "build": {"appId": "com.tuempresa.tuapp", "productName": "Tu Aplicación", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "!node_modules/.cache", "!node_modules/.vite", "!**/*.map"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "forceCodeSigning": false, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "compression": "maximum", "npmRebuild": false, "buildDependenciesFromSource": false}}