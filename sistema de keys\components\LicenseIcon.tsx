import React, { useState } from 'react';
import { LicenseInfo } from './LicenseInfo';

interface LicenseIconProps {
  className?: string;
  onDeactivate?: () => void;
}

export const LicenseIcon: React.FC<LicenseIconProps> = ({ className = '', onDeactivate }) => {
  const [showLicenseInfo, setShowLicenseInfo] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowLicenseInfo(true)}
        className={`group relative p-2 rounded-md hover:bg-gray-700 transition-colors ${className}`}
        title="Ver información de licencia"
      >
        {/* Icono de llave */}
        <svg 
          className="w-5 h-5 text-gray-400 group-hover:text-yellow-400 transition-colors" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m0 0a2 2 0 102 2m-2-2a2 2 0 012 2m0 0V9a2 2 0 002-2m-2 2H7a2 2 0 00-2-2" 
          />
        </svg>
        
        {/* Indicador de estado activo */}
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
        
        {/* Tooltip */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
          Información de Licencia
        </div>
      </button>

      {/* Modal de información de licencia */}
      <LicenseInfo 
        isOpen={showLicenseInfo} 
        onClose={() => setShowLicenseInfo(false)}
        onDeactivate={onDeactivate}
      />
    </>
  );
};
