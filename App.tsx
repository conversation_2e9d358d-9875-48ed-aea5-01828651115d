
import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Toolbar } from './components/Toolbar';
import { Canvas } from './components/Canvas';
import { LayersPanel } from './components/LayersPanel';
import { RotationControl } from './components/RotationControl';
import { ActivationDialog } from './sistema de keys/components/ActivationDialog';
import { LicenseIcon } from './sistema de keys/components/LicenseIcon';
import {
  type TokenState,
  type Annotation,
  type Tool,
  type Point,
  UnitType,
  AnnotationType,
  type DrawingState,
  type TextPreset,
  type OtherType,
  type CustomUnit,
  type ProjectData,
  type ViewTransform,
  type LayerEntry,
  type LayerItemIdentifier,
  type LayerGroup,
  type ArrowSettings,
  type ArrowAnnotation,
} from './types';
import { INITIAL_PALETTE_COLORS, FONT_FACES } from './constants';
import { translations } from './translations';

const getPathLength = (path: Point[]): number => {
    if (path.length < 2) return 0;
    return path.slice(1).reduce((acc, point, i) => acc + Math.hypot(point.x - path[i].x, point.y - path[i].y), 0);
};

const getPointOnPath = (path: Point[], progress: number): Point => {
    if (path.length < 2) return path[0] || { x: 0, y: 0 };
    const totalLength = getPathLength(path);
    if(totalLength === 0) return path[0];

    let distanceTraveled = progress * totalLength;
    
    for (let i = 0; i < path.length - 1; i++) {
        const p1 = path[i];
        const p2 = path[i+1];
        const segmentLength = Math.hypot(p2.x - p1.x, p2.y - p1.y);
        
        if (distanceTraveled <= segmentLength) {
            const segmentProgress = segmentLength === 0 ? 0 : distanceTraveled / segmentLength;
            return {
                x: p1.x + (p2.x - p1.x) * segmentProgress,
                y: p1.y + (p2.y - p1.y) * segmentProgress
            };
        }
        distanceTraveled -= segmentLength;
    }
    return path[path.length - 1];
};

const distSq = (p1: Point, p2: Point) => (p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2;

const distToSegmentSq = (p: Point, v: Point, w: Point) => {
    const l2 = distSq(v, w);
    if (l2 === 0) return distSq(p, v);
    let t = ((p.x - v.x) * (w.x - v.x) + (p.y - v.y) * (w.y - v.y)) / l2;
    t = Math.max(0, Math.min(1, t));
    const projection = { x: v.x + t * (w.x - v.x), y: v.y + t * (w.y - v.y) };
    return distSq(p, projection);
};

interface NewTextState {
    content: string;
    size: number; // in rem
    fontFamily: string;
    outlineColor1: string;
    outlineColor2: string;
    outlineWidth: number;
}

interface HistoryState {
    tokens: TokenState[];
    annotations: Annotation[];
    layers: LayerEntry[];
    customUnits: CustomUnit[];
    customOthers: CustomUnit[];
}

const toolShortcuts: { [key: string]: Tool } = {
  s: 'select',
  m: 'move',
  z: 'zoom',
  e: 'enlarge',
  t: 'text',
  b: 'eraser',
  f: 'arrow',
  c: 'circle',
  d: 'clone',
};

// Helper to find a group recursively
const findGroupRec = (entries: LayerEntry[], id: number): LayerGroup | null => {
    for (const entry of entries) {
        if (entry.type === 'group') {
            if (entry.id === id) return entry;
            const found = findGroupRec(entry.items, id);
            if (found) return found;
        }
    }
    return null;
};

// Helper to get all items (tokens/annotations) from a group, including nested groups
const getAllItemIdsFromGroup = (group: LayerGroup): { tokens: Set<number>, annotations: Set<number> } => {
    const ids: { tokens: Set<number>, annotations: Set<number> } = { tokens: new Set(), annotations: new Set() };
    const collect = (items: LayerEntry[]) => {
        for (const item of items) {
            if (item.type === 'group') {
                collect(item.items);
            } else if (item.type === 'token') {
                ids.tokens.add(item.id);
            } else {
                ids.annotations.add(item.id);
            }
        }
    };
    collect(group.items);
    return ids;
};


const App: React.FC = () => {
  const [mapImage, setMapImage] = useState<string | null>(null);
  const [mapDimensions, setMapDimensions] = useState<{ width: number; height: number; } | null>(null);
  const [tokens, setTokens] = useState<TokenState[]>([]);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [layers, setLayers] = useState<LayerEntry[]>([]);
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [redoStack, setRedoStack] = useState<HistoryState[]>([]);
  const [viewTransform, setViewTransform] = useState<ViewTransform | null>(null);
  const [language, setLanguage] = useState<'en' | 'es'>('es');
  const [mapRotation, setMapRotation] = useState<number>(0);
  const [isMapRotating, setIsMapRotating] = useState<boolean>(false);

  // Sistema de activación
  const [isActivated, setIsActivated] = useState<boolean>(() => {
    return localStorage.getItem('app_activated') === 'true';
  });

  const t = useCallback((key: keyof typeof translations): string => {
    const translation = translations[key];
    if (translation) {
      return translation[language] || key;
    }
    return key;
  }, [language]);

  // Funciones del sistema de activación
  const handleActivationSuccess = useCallback(() => {
    setIsActivated(true);
    localStorage.setItem('app_activated', 'true');
    localStorage.setItem('activation_date', new Date().toISOString());
  }, []);

  const handleDeactivate = useCallback(() => {
    localStorage.removeItem('app_activated');
    localStorage.removeItem('activation_key');
    localStorage.removeItem('activation_date');
    setIsActivated(false);
  }, []);

  const [activeTool, setActiveTool] = useState<Tool>('select');
  const [selectedTokenId, setSelectedTokenId] = useState<number | null>(null);
  const [drawingState, setDrawingState] = useState<DrawingState | null>(null);
  const [paletteColors, setPaletteColors] = useState<string[]>(INITIAL_PALETTE_COLORS);
  const [selectedColor1, setSelectedColor1] = useState<string>(INITIAL_PALETTE_COLORS[0]);
  const [selectedColor2, setSelectedColor2] = useState<string>(INITIAL_PALETTE_COLORS[1]);
  const [editingTokenId, setEditingTokenId] = useState<number | null>(null);
  const [newText, setNewText] = useState<NewTextState>(() => ({
    content: t('text_creator_default_text'),
    size: 1.5, // rem
    fontFamily: FONT_FACES[0],
    outlineColor1: '#FFFFFF',
    outlineColor2: '#000080',
    outlineWidth: 0.15,
  }));
  const [lastOutlineWidth, setLastOutlineWidth] = useState<number>(0.15);
  const [textPresets, setTextPresets] = useState<TextPreset[]>(() => {
    try {
      const saved = localStorage.getItem('textPresets');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Failed to load text presets from localStorage', error);
      return [];
    }
  });
  const [arrowSettings, setArrowSettings] = useState<ArrowSettings>({
    color: INITIAL_PALETTE_COLORS[0],
    strokeWidthStart: 12.5,
    strokeWidthEnd: 4.5,
    arrowheadLength: 17,
    arrowheadWidth: 18,
    isAnimated: true,
    animatingCircleRadius: 6,
    animatingCircleColor: '#5B9DFF',
    animationDuration: 3.0,
  });
  const [customUnits, setCustomUnits] = useState<CustomUnit[]>([]);
  const [customOthers, setCustomOthers] = useState<CustomUnit[]>([]);
  const [globalTokenSize, setGlobalTokenSize] = useState<number>(0);
  const [arePathsVisible, setArePathsVisible] = useState<boolean>(true);

  const nextId = useRef(1);
  const animationFrameId = useRef<number | null>(null);
  const lastClick = useRef<{ time: number; tokenId: number | null }>({ time: 0, tokenId: null });
  const canvasRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Update default text when language changes, only if it's still the default
    if (newText.content === translations.text_creator_default_text.en || newText.content === translations.text_creator_default_text.es) {
      setNewText(nt => ({...nt, content: t('text_creator_default_text')}));
    }
  }, [t, newText.content]);

  useEffect(() => {
    try {
      localStorage.setItem('textPresets', JSON.stringify(textPresets));
    } catch (error) {
      console.error('Failed to save text presets to localStorage', error);
    }
  }, [textPresets]);

  useEffect(() => {
    if (selectedTokenId !== null) {
      setTokens(currentTokens => {
        const token = currentTokens.find(t => t.id === selectedTokenId);
        if (!token || token.type === UnitType.Text) return currentTokens;

        const needsUpdate = token.color !== selectedColor1 || 
                            token.outlineColor1 !== selectedColor1 ||
                            token.outlineColor2 !== selectedColor2;

        if (needsUpdate) {
          return currentTokens.map(t =>
            t.id === selectedTokenId
              ? { ...t, color: selectedColor1, outlineColor1: selectedColor1, outlineColor2: selectedColor2 }
              : t
          );
        }
        return currentTokens;
      });
    }
  }, [selectedColor1, selectedColor2, selectedTokenId]);

  const getNextId = useCallback(() => {
    nextId.current += 1;
    return nextId.current;
  }, []);

  const saveStateToHistory = useCallback(() => {
    setHistory(prev => [...prev, { tokens, annotations, layers, customUnits, customOthers }]);
    setRedoStack([]);
  }, [tokens, annotations, layers, customUnits, customOthers]);

  const handleUndo = useCallback(() => {
    if (history.length === 0) return;

    const lastState = history[history.length - 1];
    const newHistory = history.slice(0, history.length - 1);

    setRedoStack(prev => [...prev, { tokens, annotations, layers, customUnits, customOthers }]);
    
    setTokens(lastState.tokens);
    setAnnotations(lastState.annotations);
    setLayers(lastState.layers);
    setCustomUnits(lastState.customUnits);
    setCustomOthers(lastState.customOthers);
    setHistory(newHistory);

    setSelectedTokenId(null);
    setDrawingState(null);
    setEditingTokenId(null);
    setActiveTool('select');
  }, [history, tokens, annotations, layers, customUnits, customOthers]);

  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;

    const nextState = redoStack[redoStack.length - 1];
    const newRedoStack = redoStack.slice(0, redoStack.length - 1);

    setHistory(prev => [...prev, { tokens, annotations, layers, customUnits, customOthers }]);

    setTokens(nextState.tokens);
    setAnnotations(nextState.annotations);
    setLayers(nextState.layers);
    setCustomUnits(nextState.customUnits);
    setCustomOthers(nextState.customOthers);
    setRedoStack(newRedoStack);

    setSelectedTokenId(null);
    setDrawingState(null);
    setEditingTokenId(null);
    setActiveTool('select');
  }, [redoStack, tokens, annotations, layers, customUnits, customOthers]);

  useEffect(() => {
    setDrawingState(null);
  }, [activeTool]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement;
      const isEditing = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;

      if (e.key === 'Escape') {
        setDrawingState(null);
        setEditingTokenId(null);
        setViewTransform(null);
        return;
      }

      if (isEditing) return;

      const key = e.key.toLowerCase();
      const isUndo = (e.ctrlKey || e.metaKey) && key === 'z' && !e.shiftKey;
      const isRedo = (e.ctrlKey || e.metaKey) && (key === 'y' || (key === 'z' && e.shiftKey));

      if (isUndo) {
        e.preventDefault();
        handleUndo();
      } else if (isRedo) {
        e.preventDefault();
        handleRedo();
      } else if (toolShortcuts[key]) {
        e.preventDefault();
        setActiveTool(toolShortcuts[key]);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleUndo, handleRedo]);

  const handleMapUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        if (!imageUrl) return;

        const img = new Image();
        img.onload = () => {
          saveStateToHistory();
          setMapImage(imageUrl);
          setMapDimensions({ width: img.naturalWidth, height: img.naturalHeight });
          setTokens([]);
          setAnnotations([]);
          setLayers([]);
          setSelectedTokenId(null);
          setDrawingState(null);
        };
        img.src = imageUrl;
      };
      reader.readAsDataURL(event.target.files[0]);
    }
  };

  const addToken = (type: UnitType, position: Point, options: Partial<TokenState> = {}) => {
    saveStateToHistory();
    const newId = getNextId();
    const newToken: TokenState = {
      id: newId,
      type,
      color: options.color || selectedColor1,
      number: null,
      position,
      path: [],
      animationProgress: 0,
      patrolForward: true,
      size: options.size ?? 1,
      rotation: options.rotation ?? 0,
      text: type === UnitType.Text ? options.text ?? 'Text' : undefined,
      fontFamily: type === UnitType.Text ? options.fontFamily : undefined,
      outlineColor1: options.outlineColor1 ?? selectedColor1,
      outlineColor2: options.outlineColor2 ?? selectedColor2,
      outlineWidth: options.outlineWidth ?? lastOutlineWidth,
      otherType: type === UnitType.Other ? options.otherType : undefined,
      customImage: type === UnitType.Custom || type === UnitType.CustomOther ? options.customImage : undefined,
      isVisible: true,
      animationSpeed: options.animationSpeed ?? 0.20,
      isPatrol: options.isPatrol ?? false,
      isFlipped: options.isFlipped ?? false,
      isAnimating: true,
      name: options.name,
    };
    setTokens((prev) => [...prev, newToken]);
    setLayers(prev => [{id: newId, type: 'token'}, ...prev]);
    return newToken;
  }

  const handleTokenDrop = (data: string, position: Point) => {
    if (!mapDimensions) return;
    let parsedData;
    try {
        parsedData = JSON.parse(data);
    } catch (e) {
        console.error("Failed to parse dropped data:", e);
        return;
    }

    if (parsedData.isTextToken) {
        // Text tokens get their data from a different source state (newText)
        addToken(UnitType.Text, position, {
            text: parsedData.content,
            size: parsedData.size,
            fontFamily: parsedData.fontFamily,
            color: selectedColor1,
            outlineColor1: parsedData.outlineColor1,
            outlineColor2: parsedData.outlineColor2,
            outlineWidth: parsedData.outlineWidth,
        });
    } else if (Object.values(UnitType).includes(parsedData.type)) {
        // All other unit types can use this generic logic
        const { type, ...options } = parsedData;
        addToken(type, position, { 
            ...options, // This includes customImage, name, otherType, rotation, outlineWidth etc.
            color: selectedColor1,
            outlineColor1: selectedColor1,
            outlineColor2: selectedColor2
        });
    }
  };

  const deleteItem = useCallback((id: number, type: 'token' | 'annotation') => {
    saveStateToHistory();
    if (type === 'token') {
        setTokens(prev => prev.filter(t => t.id !== id));
        if (selectedTokenId === id) setSelectedTokenId(null);
        if (editingTokenId === id) setEditingTokenId(null);
    } else {
        setAnnotations(prev => prev.filter(a => a.id !== id));
    }

    const removeItemRec = (entries: LayerEntry[]): LayerEntry[] => {
      return entries.reduce((acc, entry) => {
          if (entry.type === 'group') {
              const newItems = removeItemRec(entry.items || []); // Guard against undefined items
              if (newItems.length > 0) {
                   acc.push({ ...entry, items: newItems });
              }
          } else if (!(entry.id === id && entry.type === type)) {
              acc.push(entry);
          }
          return acc;
      }, [] as LayerEntry[]);
    };

    setLayers(prev => removeItemRec(prev));

  }, [selectedTokenId, editingTokenId, saveStateToHistory]);

  const handleZoom = useCallback((s1: Point, s2: Point, canvasRect: DOMRect | undefined) => {
    if (!canvasRect) return;

    const zoomRectWidth = Math.abs(s1.x - s2.x);
    const zoomRectHeight = Math.abs(s1.y - s2.y);
    
    // This is the scale factor needed to map the selection to the canvas
    const scaleX = canvasRect.width / zoomRectWidth;
    const scaleY = canvasRect.height / zoomRectHeight;
    const scale_map = Math.min(scaleX, scaleY, 8 / (viewTransform?.scale ?? 1)); // Cap final zoom at 8x
    
    // This is the translation needed to map the selection to the canvas
    const zoomCenterX_screen = Math.min(s1.x, s2.x) + zoomRectWidth / 2;
    const zoomCenterY_screen = Math.min(s1.y, s2.y) + zoomRectHeight / 2;
    const translate_map_x = (canvasRect.width / 2) - (zoomCenterX_screen * scale_map);
    const translate_map_y = (canvasRect.height / 2) - (zoomCenterY_screen * scale_map);

    // Compose with the existing viewTransform
    const old_scale = viewTransform?.scale ?? 1;
    const old_translateX = viewTransform?.translateX ?? 0;
    const old_translateY = viewTransform?.translateY ?? 0;

    const new_scale = scale_map * old_scale;
    const new_translateX = scale_map * old_translateX + translate_map_x;
    const new_translateY = scale_map * old_translateY + translate_map_y;

    setViewTransform({ scale: new_scale, translateX: new_translateX, translateY: new_translateY });

  }, [viewTransform]);

  const handleZoomReset = useCallback(() => {
    setViewTransform(null);
  }, []);

  const handleMouseDown = (point: Point, screenPoint: Point) => {
    if (!mapDimensions) return;
    
    const TOKEN_HIT_RADIUS = 20;

    if (activeTool === 'zoom') {
        setDrawingState({ type: 'zoom', start: screenPoint, current: screenPoint });
        return;
    }
    
    if (activeTool === 'enlarge') {
        const tokenToEnlarge = [...tokens].reverse().find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        if (tokenToEnlarge) {
            saveStateToHistory();
            setDrawingState({
                type: 'enlarging',
                tokenId: tokenToEnlarge.id,
                startY: point.y,
                startSize: tokenToEnlarge.size ?? 1,
                current: point
            });
        }
        return;
    }

    if (activeTool === 'move' || activeTool === 'clone') {
        const tokenToInteract = [...tokens].reverse().find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        if (tokenToInteract) {
            if (activeTool === 'move') {
                saveStateToHistory();
                const offset = { x: point.x - tokenToInteract.position.x, y: point.y - tokenToInteract.position.y };
                setDrawingState({ type: 'moving', tokenId: tokenToInteract.id, offset, current: point });
            } else { // clone
                setDrawingState({ type: 'cloning', tokenToClone: tokenToInteract, current: point });
            }
        }
        return;
    }

    if (activeTool === 'eraser') {
      const tokenToDelete = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
      if (tokenToDelete) {
        deleteItem(tokenToDelete.id, 'token');
        return;
      }
      
      const annotationToDelete = annotations.find(a => {
        if (a.isVisible === false) return false;
        if (a.type === AnnotationType.Circle) return distSq(point, a.center) <= a.radius * a.radius;
        if (a.type === AnnotationType.Arrow) {
            const HIT_RADIUS_SQ = (TOKEN_HIT_RADIUS/2) ** 2;
            return distToSegmentSq(point, a.start, a.control) < HIT_RADIUS_SQ ||
                   distToSegmentSq(point, a.control, a.end) < HIT_RADIUS_SQ;
        }
        return false;
      });

      if (annotationToDelete) {
        deleteItem(annotationToDelete.id, 'annotation');
        return;
      }

       const tokenWithPath = tokens.find(token => {
        if (!token.path || token.path.length < 2 || token.isVisible === false) return false;
        for (let i = 0; i < token.path.length - 1; i++) {
          if (distToSegmentSq(point, token.path[i], token.path[i+1]) < (TOKEN_HIT_RADIUS/2)**2) return true;
        }
        return false;
      });

      if (tokenWithPath) clearPath(tokenWithPath.id);
      return;
    }

    if (activeTool === 'select') {
        const clickedToken = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        
        if (clickedToken) {
            const now = Date.now();
            const isDoubleClick = clickedToken.type === UnitType.Text && 
                                  now - lastClick.current.time < 300 && 
                                  lastClick.current.tokenId === clickedToken.id;
    
            if (isDoubleClick) {
                // Double-click to edit text
                lastClick.current = { time: 0, tokenId: null }; // Reset click tracker
                setEditingTokenId(clickedToken.id);
                setSelectedTokenId(null); // Deselect to avoid confusion with path drawing
                setActiveTool('select'); // Ensure tool remains 'select' for editing.
            } else {
                // Single-click to select any token for path drawing
                lastClick.current = { time: now, tokenId: clickedToken.id };
                setSelectedTokenId(clickedToken.id);
                setEditingTokenId(null);
                setActiveTool('path');
            }
        } else {
            // Clicked on empty space
            setSelectedTokenId(null);
            setEditingTokenId(null);
        }
        return;
    }
    
    if (activeTool === 'arrow') {
        if (drawingState?.type === 'arrow' && drawingState.stage === 'defining-control') {
            saveStateToHistory();
            const { start, end, current } = drawingState;
            const newId = getNextId();
            const newArrow: ArrowAnnotation = { 
                id: newId, 
                type: AnnotationType.Arrow, 
                start, 
                end, 
                control: current, 
                color: arrowSettings.color, 
                isVisible: true,
                ...arrowSettings
            };
            setAnnotations(prev => [...prev, newArrow]);
            setLayers(prev => [{id: newId, type: 'annotation'}, ...prev]);
            setDrawingState(null);
        } else {
            setDrawingState({ type: 'arrow', stage: 'defining-end', start: point, current: point });
        }
    } else if (activeTool === 'path' && selectedTokenId !== null) {
        saveStateToHistory();
        setDrawingState({ type: 'path', pathForTokenId: selectedTokenId, points: [point] });
    } else if (activeTool === 'circle') {
        setDrawingState({ type: 'circle', start: point, current: point });
    }
  };

  const handleMouseMove = (point: Point, screenPoint: Point) => {
    if (!drawingState) return;

    if (drawingState.type === 'zoom') {
        setDrawingState(prev => (prev?.type === 'zoom' ? { ...prev, current: screenPoint } : prev));
        return;
    }

    if (drawingState.type === 'path') {
        const lastPoint = drawingState.points[drawingState.points.length - 1];
        if (distSq(lastPoint, point) > 5 * 5) { // 5px distance threshold
            setDrawingState(prev => {
                if (prev?.type !== 'path') return prev;
                return { ...prev, points: [...prev.points, point] };
            });
        }
        return;
    }

    if (drawingState.type === 'enlarging') {
        const { tokenId, startY, startSize } = drawingState;
        const deltaY = point.y - startY;
        const SENSITIVITY = 0.01;
        const newSize = Math.max(0.2, Math.min(10.0, startSize - deltaY * SENSITIVITY));
        setTokens(currentTokens =>
            currentTokens.map(t => (t.id === tokenId ? { ...t, size: newSize } : t))
        );
    } else if (drawingState.type === 'moving') {
        const { tokenId, offset } = drawingState;
        const newPosition = { x: point.x - offset.x, y: point.y - offset.y };
        setTokens(currentTokens =>
            currentTokens.map(t => (t.id === tokenId ? { ...t, position: newPosition } : t))
        );
    }
    
    setDrawingState(prev => (prev ? { ...prev, current: point } : null));
  };
  
  const handleMouseUp = (canvasRect: DOMRect | undefined) => {
    if (!drawingState) return;
    
    if (drawingState.type === 'path') {
        const { pathForTokenId, points } = drawingState;
        if (points.length > 1) { // Only add if the user actually drew something
            setTokens(prevTokens => prevTokens.map(token => {
                if (token.id === pathForTokenId) {
                    const newPath = token.path.length === 0 
                        ? [token.position, ...points] 
                        : [...token.path, ...points];
                    return { ...token, path: newPath };
                }
                return token;
            }));
        }
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'zoom') {
        const { start, current } = drawingState; // These are screen points
        const dx = Math.abs(start.x - current.x);
        const dy = Math.abs(start.y - current.y);
        // Ensure zoom area is not too small (in screen pixels)
        if (dx > 10 && dy > 10) {
            handleZoom(start, current, canvasRect);
        }
        setDrawingState(null);
        return;
    }
    
    if (drawingState.type === 'enlarging') {
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'moving') {
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'cloning') {
        saveStateToHistory();
        const { tokenToClone, current } = drawingState;
        const { id, path, animationProgress, patrolForward, ...options } = tokenToClone;
        addToken(options.type, current, options);
        setDrawingState(null);
        return;
    }
    
    if (drawingState.type === 'arrow' && drawingState.stage === 'defining-end') {
        if (distSq(drawingState.start, drawingState.current) < 5 * 5) {
            setDrawingState(null);
            return;
        }
        const { start, current } = drawingState;
        const control = { x: (start.x + current.x) / 2, y: (start.y + current.y) / 2 };
        setDrawingState({ type: 'arrow', stage: 'defining-control', start, end: current, current: control });
        return;
    }

    const { type, start, current } = drawingState;
     if (distSq(start, current) < 5 * 5) {
        setDrawingState(null);
        return;
    }

    if (type === 'circle') {
        saveStateToHistory();
        const radius = Math.hypot(current.x - start.x, current.y - start.y);
        const newId = getNextId();
        setAnnotations(prev => [...prev, { id: newId, type: AnnotationType.Circle, center: start, radius, color: selectedColor1, isVisible: true }]);
        setLayers(prev => [{id: newId, type: 'annotation'}, ...prev]);
    }
    setDrawingState(null);
  };
  
  const handleTokenUpdate = (tokenId: number, updates: Partial<TokenState>) => {
    if (updates.outlineWidth !== undefined) {
        setLastOutlineWidth(updates.outlineWidth);
    }
    setTokens(prev => {
        const needsHistory = !Object.keys(updates).every(k => ['animationProgress', 'position', 'patrolForward'].includes(k));
        if(needsHistory) saveStateToHistory();
        return prev.map(t => {
            if (t.id !== tokenId) return t;

            const newT = { ...t, ...updates };
            if (updates.outlineColor2 === undefined && 'outlineColor2' in updates) {
                delete newT.outlineColor2;
            }
            return newT;
        });
    });
  }
  
  const handleWheel = (e: React.WheelEvent, point: Point) => {
    if (!mapDimensions) return;
    const TOKEN_HIT_RADIUS = 20;
    const tokenToUpdate = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
    if (!tokenToUpdate) return;
    
    e.preventDefault();
    
    if (e.ctrlKey) {
       handleTokenUpdate(tokenToUpdate.id, { size: Math.max(0.2, Math.min(10.0, (tokenToUpdate.size ?? 1) - Math.sign(e.deltaY) * 0.1)) });
    } else if(tokenToUpdate.type !== UnitType.Text && tokenToUpdate.type !== UnitType.Other) {
      let currentNum = tokenToUpdate.number ?? (e.deltaY < 0 ? 0 : 2);
      if (e.deltaY < 0) {
        currentNum = currentNum >= 15 ? 1 : currentNum + 1;
      } else {
        currentNum = currentNum <= 1 ? 15 : currentNum - 1;
      }
      handleTokenUpdate(tokenToUpdate.id, { number: currentNum });
    }
  };

  const animationLoop = useCallback(() => {
    setTokens(currentTokens =>
      currentTokens.map(token => {
        if (token.path.length < 2 || token.isVisible === false || token.isAnimating === false) return token;

        const isPatrol = token.isPatrol ?? false;
        const animationSpeed = token.animationSpeed ?? 1.0;

        const pathLength = getPathLength(token.path);
        if (pathLength === 0) return token;
        
        const baseSpeed = 2; // map units per frame at 1x speed
        const progressIncrement = (baseSpeed * Math.abs(animationSpeed)) / pathLength;

        let newProgress = token.animationProgress;
        let isForward = token.patrolForward ?? true;

        if (isPatrol) {
          if (isForward) {
            newProgress += progressIncrement;
            if (newProgress >= 1.0) {
              newProgress = 1.0;
              isForward = false;
            }
          } else {
            newProgress -= progressIncrement;
            if (newProgress <= 0.0) {
              newProgress = 0.0;
              isForward = true;
            }
          }
        } else {
          // Non-patrol mode: infinite loop from start to end.
          newProgress += progressIncrement;
          if (newProgress >= 1.0) {
            newProgress %= 1.0; // Loop back to the beginning.
          }
        }

        const newPosition = getPointOnPath(token.path, newProgress);

        return { ...token, animationProgress: newProgress, position: newPosition, patrolForward: isForward };
      })
    );
    animationFrameId.current = requestAnimationFrame(animationLoop);
  }, []);

  useEffect(() => {
    animationFrameId.current = requestAnimationFrame(animationLoop);
    return () => {
        if(animationFrameId.current) cancelAnimationFrame(animationFrameId.current);
    };
  }, [animationLoop]);

  const clearPath = (tokenId: number) => {
    saveStateToHistory();
    setTokens(prev => prev.map(t => {
      if (t.id === tokenId) {
        const originalPosition = t.path.length > 0 ? t.path[0] : t.position;
        return { ...t, path: [], animationProgress: 0, position: originalPosition, patrolForward: true };
      }
      return t;
    }));
    if (selectedTokenId === tokenId) {
      setSelectedTokenId(null);
      setActiveTool('select');
    }
  };

  const clearAll = () => {
    saveStateToHistory();
    setTokens([]);
    setAnnotations([]);
    setLayers([]);
    setMapImage(null);
    setMapDimensions(null);
    setSelectedTokenId(null);
    setActiveTool('select');
  }

  const handleAddTextPreset = useCallback(() => {
    if (textPresets.some(p => p.content === newText.content && p.fontFamily === newText.fontFamily && p.size === newText.size)) {
        return;
    }
    const preset: TextPreset = { id: Date.now(), ...newText };
    setTextPresets(prev => [...prev, preset]);
  }, [newText, textPresets]);

  const handleDeleteTextPreset = useCallback((id: number) => {
    setTextPresets(prev => prev.filter(p => p.id !== id));
  }, []);

  const handleLoadTextPreset = useCallback((preset: TextPreset) => {
    setNewText({
        ...newText,
        content: preset.content,
        size: preset.size,
        fontFamily: preset.fontFamily,
    });
  }, [newText]);

  const handlePaletteColorChange = (index: number, newColor: string) => {
      saveStateToHistory();
      const oldColor = paletteColors[index];
      const newPalette = [...paletteColors];
      newPalette[index] = newColor;
      setPaletteColors(newPalette);

      if (selectedColor1 === oldColor) {
        setSelectedColor1(newColor);
      }
      if (selectedColor2 === oldColor) {
        setSelectedColor2(newColor);
      }
  };

  const handleNewTextChange = (state: NewTextState) => {
    if (state.outlineWidth !== newText.outlineWidth) {
        setLastOutlineWidth(state.outlineWidth);
    }
    setNewText(state);
  };
  
  const handleToggleVisibility = (id: number, type: 'token' | 'annotation') => {
      saveStateToHistory();
      if (type === 'token') {
          setTokens(prev => prev.map(t => t.id === id ? { ...t, isVisible: !(t.isVisible ?? true) } : t));
      } else {
          setAnnotations(prev => prev.map(a => a.id === id ? { ...a, isVisible: !(a.isVisible ?? true) } : a));
      }
  };

  const handleToggleGroupVisibility = useCallback((groupId: number) => {
      saveStateToHistory();
      const group = findGroupRec(layers, groupId);
      if (!group) return;
  
      const { tokens: tokenIds, annotations: annotationIds } = getAllItemIdsFromGroup(group);
  
      // Check if any item in the group is currently visible
      const isAnyVisible = 
          Array.from(tokenIds).some(id => tokens.find(t => t.id === id)?.isVisible ?? true) ||
          Array.from(annotationIds).some(id => annotations.find(a => a.id === id)?.isVisible ?? true);
          
      const newVisibility = !isAnyVisible;
  
      setTokens(prev => prev.map(t => tokenIds.has(t.id) ? { ...t, isVisible: newVisibility } : t));
      setAnnotations(prev => prev.map(a => annotationIds.has(a.id) ? { ...a, isVisible: newVisibility } : a));
  }, [layers, tokens, annotations, saveStateToHistory]);

  const handleLayersUpdate = (newLayers: LayerEntry[]) => {
      saveStateToHistory();
      setLayers(newLayers);
  };
  
  const handleCustomUnitUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    Array.from(files).forEach(file => {
        if (file.type === 'image/png' || file.type === 'image/gif') {
            const reader = new FileReader();
            const filename = file.name;
            const name = filename.substring(0, filename.lastIndexOf('.')) || filename;
            reader.onload = (e) => {
                const imageData = e.target?.result as string;
                if (imageData) {
                    setCustomUnits(prev => [...prev, { id: getNextId(), name, imageData }]);
                }
            };
            reader.readAsDataURL(file);
        }
    });

    if (event.target) {
        event.target.value = '';
    }
  };
  
  const handleCustomOtherUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    Array.from(files).forEach(file => {
        if (file.type === 'image/png' || file.type === 'image/gif') {
            const reader = new FileReader();
            const filename = file.name;
            const name = filename.substring(0, filename.lastIndexOf('.')) || filename;
            reader.onload = (e) => {
                const imageData = e.target?.result as string;
                if (imageData) {
                    setCustomOthers(prev => [...prev, { id: getNextId(), name, imageData }]);
                }
            };
            reader.readAsDataURL(file);
        }
    });

    if (event.target) {
        event.target.value = '';
    }
  };

  const handleDeleteCustomUnit = useCallback((id: number) => {
    saveStateToHistory();
    setCustomUnits(prev => prev.filter(unit => unit.id !== id));
  }, [saveStateToHistory]);

  const handleDeleteCustomOther = useCallback((id: number) => {
    saveStateToHistory();
    setCustomOthers(prev => prev.filter(unit => unit.id !== id));
  }, [saveStateToHistory]);

  const handleExportProject = useCallback(() => {
    const projectData: ProjectData = {
      mapImage,
      mapDimensions,
      tokens,
      annotations,
      paletteColors,
      nextId: nextId.current,
      customUnits,
      customOthers,
      layers,
    };

    const jsonString = JSON.stringify(projectData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'battle-plan.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [mapImage, mapDimensions, tokens, annotations, paletteColors, customUnits, customOthers, layers]);

  const handleImportProject = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') throw new Error('File is not a text file.');
        
        const data: Partial<ProjectData & { customArtillery?: CustomUnit[] }> = JSON.parse(text);

        if (!data || typeof data !== 'object') {
          throw new Error('Invalid project file format.');
        }
        
        const migratedTokens = (data.tokens || []).map(t => {
            const { auraSize, auraOpacity, auraColor2, artilleryType, ...rest } = t as any; // remove old props
            const token = {
              ...rest,
              isVisible: rest.isVisible ?? true,
              rotation: rest.rotation ?? 0,
              animationSpeed: rest.animationSpeed ?? 1.0,
              isPatrol: rest.isPatrol ?? false,
              isFlipped: rest.isFlipped ?? false,
              isAnimating: rest.isAnimating ?? true,
            };
            if (artilleryType) {
                (token as TokenState).otherType = artilleryType;
            }
            return token as TokenState;
        });
        
        const migratedAnnotations = (data.annotations || []).map(a => {
            if (a.type === AnnotationType.Arrow) {
                const oldArrow = a as any;
                const { strokeWidth, ...rest } = oldArrow;
                const strokeWidthStart = strokeWidth ?? oldArrow.strokeWidthStart ?? 5;
                const strokeWidthEnd = oldArrow.strokeWidthEnd ?? strokeWidthStart;
                
                return {
                    ...{ // Default values for properties that might be missing on old arrows
                        arrowheadLength: 15,
                        arrowheadWidth: 10,
                        isAnimated: true,
                        animatingCircleRadius: 4,
                        animatingCircleColor: a.color,
                        animationDuration: 2.5,
                    },
                    ...rest,
                    isVisible: a.isVisible ?? true,
                    strokeWidthStart,
                    strokeWidthEnd,
                };
            }
            return { ...a, isVisible: a.isVisible ?? true };
        });

        setHistory([]);
        setRedoStack([]);
        setTokens(migratedTokens);
        setAnnotations(migratedAnnotations);

        if (data.layers) {
            setLayers(data.layers);
        } else {
            const legacyLayers: LayerEntry[] = [
                ...migratedAnnotations.map(a => ({ id: a.id, type: 'annotation' as const })),
                ...migratedTokens.map(t => ({ id: t.id, type: 'token' as const })),
            ].reverse();
            setLayers(legacyLayers);
        }
        
        setSelectedTokenId(null);
        setDrawingState(null);
        setEditingTokenId(null);
        setActiveTool('select');
        setViewTransform(null);

        setMapImage(data.mapImage ?? null);
        setMapDimensions(data.mapDimensions ?? null);
        setPaletteColors(data.paletteColors ?? INITIAL_PALETTE_COLORS);
        setCustomUnits(data.customUnits ?? []);
        setCustomOthers(data.customOthers ?? data.customArtillery ?? []);
        nextId.current = data.nextId ?? Math.max(0, ...migratedTokens.map(t => t.id), ...migratedAnnotations.map(a => a.id)) + 1;

        if (event.target) {
            event.target.value = '';
        }
      } catch (error) {
        console.error('Failed to import project:', error);
        alert(t('project_import_error'));
        if (event.target) {
            event.target.value = '';
        }
      }
    };
    reader.readAsText(file);
  };

  const selectedToken = tokens.find(t => t.id === selectedTokenId) ?? null;
  
  const flattenedItemsForCanvas = useMemo(() => {
    const finalItemMap = new Map<string, TokenState | Annotation>();
    tokens.forEach(t => finalItemMap.set(`token-${t.id}`, t));
    annotations.forEach(a => finalItemMap.set(`annotation-${a.id}`, a));

    const flatList: LayerItemIdentifier[] = [];
    const flatten = (items: LayerEntry[]) => {
      for (const item of items) {
        if (item.type === 'group') {
          // Always recurse into groups to get all items for canvas rendering.
          // The isCollapsed property only affects the LayersPanel UI.
          flatten(item.items);
        } else {
          flatList.push(item);
        }
      }
    };
    flatten(layers);

    return flatList
      .map(identifier => finalItemMap.get(`${identifier.type}-${identifier.id}`))
      .filter((item): item is TokenState | Annotation => !!item);
  }, [layers, tokens, annotations]);

  const [canvasTokens, canvasAnnotations] = useMemo(() => {
        const tkns: TokenState[] = [];
        const anns: Annotation[] = [];
        for (const item of flattenedItemsForCanvas) {
            if (item.type === AnnotationType.Arrow || item.type === AnnotationType.Circle) {
                anns.push(item as Annotation);
            } else {
                tkns.push(item as TokenState);
            }
        }
        return [tkns, anns];
  }, [flattenedItemsForCanvas]);

    const handleResetTokenAnimation = useCallback((tokenId: number) => {
        saveStateToHistory();
        setTokens(prev => prev.map(t => {
            if (t.id === tokenId && t.path.length > 0) {
                return {
                    ...t,
                    position: t.path[0],
                    animationProgress: 0,
                    patrolForward: true,
                };
            }
            return t;
        }));
    }, [saveStateToHistory]);

    const handleToggleTokenAnimation = useCallback((tokenId: number) => {
        saveStateToHistory();
        setTokens(prev => prev.map(t => {
            if (t.id === tokenId) {
                return { ...t, isAnimating: !(t.isAnimating ?? true) };
            }
            return t;
        }));
    }, [saveStateToHistory]);

    const handleResetGroupAnimation = useCallback((groupId: number) => {
        saveStateToHistory();
        const group = findGroupRec(layers, groupId);
        if (!group) return;
    
        const { tokens: tokenIdsToReset } = getAllItemIdsFromGroup(group);
    
        setTokens(prev => prev.map(t => {
            if (tokenIdsToReset.has(t.id) && t.path.length > 0) {
                return {
                    ...t,
                    position: t.path[0],
                    animationProgress: 0,
                    patrolForward: true,
                };
            }
            return t;
        }));
    }, [layers, saveStateToHistory]);

    const handleToggleGroupAnimation = useCallback((groupId: number) => {
        saveStateToHistory();
        const group = findGroupRec(layers, groupId);
        if (!group) return;
    
        const { tokens: tokenIdsInGroup } = getAllItemIdsFromGroup(group);
        if (tokenIdsInGroup.size === 0) return;
    
        const animatableTokens = tokens.filter(t => tokenIdsInGroup.has(t.id) && t.path.length > 1);
        if (animatableTokens.length === 0) return;
    
        const isAnyAnimating = animatableTokens.some(t => t.isAnimating ?? true);
        const newAnimationState = !isAnyAnimating;
    
        setTokens(prev => prev.map(t => {
            if (tokenIdsInGroup.has(t.id)) {
                return { ...t, isAnimating: newAnimationState };
            }
            return t;
        }));
    }, [layers, tokens, saveStateToHistory]);

    const handleLanguageChange = useCallback(() => {
        setLanguage(lang => (lang === 'en' ? 'es' : 'en'));
    }, []);

    const isZooming = drawingState?.type === 'zoom';

  // Si no está activado, mostrar diálogo de activación
  if (!isActivated) {
    return <ActivationDialog onActivationSuccess={handleActivationSuccess} />;
  }

  return (
    <div className="flex h-screen w-screen bg-gray-900 font-sans">
      <Toolbar 
        activeTool={activeTool}
        onToolSelect={setActiveTool}
        onMapUpload={handleMapUpload}
        onClearAll={clearAll}
        selectedColor1={selectedColor1}
        onColor1Change={setSelectedColor1}
        selectedColor2={selectedColor2}
        onColor2Change={setSelectedColor2}
        paletteColors={paletteColors}
        onPaletteColorChange={handlePaletteColorChange}
        onUndo={handleUndo}
        onRedo={handleRedo}
        newText={newText}
        onNewTextChange={handleNewTextChange}
        textPresets={textPresets}
        onAddTextPreset={handleAddTextPreset}
        onDeleteTextPreset={handleDeleteTextPreset}
        onLoadTextPreset={handleLoadTextPreset}
        arrowSettings={arrowSettings}
        onArrowSettingsChange={setArrowSettings}
        selectedToken={selectedToken}
        onTokenUpdate={handleTokenUpdate}
        onClearPath={clearPath}
        onExportProject={handleExportProject}
        onImportProject={handleImportProject}
        customUnits={customUnits}
        onCustomUnitUpload={handleCustomUnitUpload}
        onDeleteCustomUnit={handleDeleteCustomUnit}
        customOthers={customOthers}
        onCustomOtherUpload={handleCustomOtherUpload}
        onDeleteCustomOther={handleDeleteCustomOther}
        globalTokenSize={globalTokenSize}
        onGlobalTokenSizeChange={setGlobalTokenSize}
        language={language}
        onLanguageChange={handleLanguageChange}
        arePathsVisible={arePathsVisible}
        onArePathsVisibleChange={setArePathsVisible}
        t={t}
      />
      <main className="flex-1 h-full relative" ref={canvasRef}>
        <Canvas
          mapImage={mapImage}
          mapDimensions={mapDimensions}
          tokens={canvasTokens}
          annotations={canvasAnnotations}
          onDrop={handleTokenDrop}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onWheel={handleWheel}
          drawingState={drawingState}
          activeTool={activeTool}
          selectedColor={selectedColor1}
          arrowSettings={arrowSettings}
          editingTokenId={editingTokenId}
          onTokenUpdate={handleTokenUpdate}
          onFinishEditing={() => setEditingTokenId(null)}
          viewTransform={viewTransform}
          onZoomReset={handleZoomReset}
          globalTokenSize={globalTokenSize}
          arePathsVisible={arePathsVisible}
          mapRotation={mapRotation}
          isMapRotating={isMapRotating}
          t={t}
        />
        {mapImage && (
            <RotationControl 
                rotation={mapRotation}
                onRotationChange={setMapRotation}
                onRotationStart={() => setIsMapRotating(true)}
                onRotationEnd={() => setIsMapRotating(false)}
                isZooming={isZooming}
                t={t}
            />
        )}
        <LayersPanel
          layers={layers}
          tokens={tokens}
          annotations={annotations}
          selectedTokenId={selectedTokenId}
          onSelectToken={(id) => {
              setSelectedTokenId(id);
              setActiveTool(t => t === 'select' ? 'path' : t);
          }}
          onToggleVisibility={handleToggleVisibility}
          onToggleGroupVisibility={handleToggleGroupVisibility}
          onDeleteItem={deleteItem}
          onLayersUpdate={handleLayersUpdate}
          getNextId={getNextId}
          onResetTokenAnimation={handleResetTokenAnimation}
          onToggleTokenAnimation={handleToggleTokenAnimation}
          onResetGroupAnimation={handleResetGroupAnimation}
          onToggleGroupAnimation={handleToggleGroupAnimation}
          t={t}
        />
      </main>

      {/* Icono de licencia en la esquina inferior derecha */}
      <div className="fixed bottom-4 right-4 z-50">
        <LicenseIcon onDeactivate={handleDeactivate} />
      </div>
    </div>
  );
};

export default App;
