import CryptoJS from 'crypto-js';

// Clave secreta (debe coincidir con la de los componentes)
const SECRET_KEY = 'StrategyCreator2025SecretKey';

// MAC address real obtenida de Electron
const MAC_ADDRESS = 'D8:43:AE:6D:08:4E';

function generateActivationKey(macAddress) {
    try {
        // Generar HMAC SHA256
        const hmac = CryptoJS.HmacSHA256(macAddress, SECRET_KEY);
        
        // Tomar los primeros 12 caracteres y convertir a mayúsculas
        const activationKey = hmac.toString().substring(0, 12).toUpperCase();
        
        return activationKey;
    } catch (error) {
        console.error('Error generando clave:', error);
        return null;
    }
}

console.log('=== Generador de Claves de Activación ===');
console.log('Strategy Creator 2.0');
console.log('');
console.log('MAC Address:', MAC_ADDRESS);

const activationKey = generateActivationKey(MAC_ADDRESS);

if (activationKey) {
    console.log('Clave de Activación:', activationKey);
    console.log('');
    console.log('✅ Clave generada exitosamente');
    console.log('📋 Copie esta clave para activar la aplicación');
} else {
    console.log('❌ Error generando clave');
}
